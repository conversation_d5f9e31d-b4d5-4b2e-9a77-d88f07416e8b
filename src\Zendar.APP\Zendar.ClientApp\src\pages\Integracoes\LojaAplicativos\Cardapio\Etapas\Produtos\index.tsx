import { Text, VStack, Box, useMediaQuery, Flex, Icon } from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { BsArrowRight } from 'react-icons/bs';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  CategoriaProduto,
  finalizarIntegracao,
  obterCategoriasProdutos,
  utilizarTodosProdutosSistemaCardapio,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { IconeEtapaSelecaoProdutos } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas from 'constants/rotas';

import { ModalConfirmarProdutos } from '../components/ModalConfirmarProdutos';
import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import {
  opcoesDefaultTipoSelecaoProdutos,
  TipoSelecaoProdutos,
} from './validationForms';

export const Produtos = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [opcoesSelecaoProdutos, setOpcoesSelecaoProdutos] = useState(
    opcoesDefaultTipoSelecaoProdutos
  );

  const opcaoEscolhida = opcoesSelecaoProdutos.find((item) => item.isChecked);

  const history = useHistory();

  const handleAvancar = useCallback(
    (identificacao: number) => {
      setPassoAtual(identificacao);
    },
    [setPassoAtual]
  );

  const handleSelecionarCadastro = useCallback((index: number) => {
    setOpcoesSelecaoProdutos((prev) =>
      prev.map((itemCadastro, indexCadastro) => ({
        ...itemCadastro,
        isChecked: index === indexCadastro,
      }))
    );
  }, []);

  const normalizarCategoriasSelecionandoTodas = (
    categorias: CategoriaProduto[]
  ) => {
    return categorias.map((categoria) => {
      return {
        ...categoria,
        selecionado: true,
        produtos: categoria.produtos.map((produto) => ({
          ...produto,
          selecionado: true,
        })),
      };
    });
  };

  const handleSubmit = useCallback(async () => {
    setIsLoading(true);

    if (opcaoEscolhida?.value === undefined) {
      toast.warning('Escolha uma opção para avançar a etapa');
      setIsLoading(false);
      return;
    }

    if (opcaoEscolhida.value === TipoSelecaoProdutos.TODOS_PRODUTOS) {
      const todasCategoriasProdutosSistema = await obterCategoriasProdutos();
      const listaCategoriasNormalizadas = normalizarCategoriasSelecionandoTodas(
        todasCategoriasProdutosSistema
      );

      const quantidadeProdutosSelecionados =
        listaCategoriasNormalizadas?.reduce((total, categoria) => {
          const produtosSelecionados = categoria.produtos.filter(
            (p) => p.selecionado
          ).length;
          return total + produtosSelecionados;
        }, 0);

      try {
        const salvar = await ModalConfirmarProdutos({
          quantidadeProdutosSelecionados,
          todosProdutosSistema: true,
        });

        if (salvar) {
          const sucesso = await utilizarTodosProdutosSistemaCardapio(
            listaCategoriasNormalizadas
          );

          if (sucesso) {
            const finalizado = await finalizarIntegracao();
            if (finalizado) {
              toast.success('Integração finalizada com sucesso!');
              history.push(ConstanteRotas.CARDAPIO_VENDAS_PAINEL);
            } else {
              toast.warn(
                'Não foi possível finalizar a configuração da integração'
              );
            }
            setIsLoading(false);
            return;
          }
        }
      } catch (error) {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
      handleAvancar(ETAPA_GUIA_INTEGRACAO_CARDAPIO.SELECIONAR_PRODUTOS);
    }

    setIsLoading(false);
  }, [handleAvancar, opcaoEscolhida, setIsLoading]);

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.MIDIAS_SOCIAIS);
  };

  return (
    <>
      <ContainerIntegracaoFixo height="100vh">
        <Header
          title="Selecionar produtos"
          handleVoltar={handleVoltar}
          icon={IconeEtapaSelecaoProdutos}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack fontSize="14px" maxW="600px" alignItems="left" spacing="24px">
            {opcoesSelecaoProdutos.map((cadastro, index) => (
              <Flex
                borderWidth="1px"
                borderStyle="solid"
                opacity={cadastro.isDisable ? '0.6' : '1'}
                borderColor={
                  cadastro.isChecked ? 'rgba(85, 2, 178, 0.5)' : 'gray.50'
                }
                _hover={{
                  borderColor: cadastro.isDisable
                    ? 'none'
                    : 'rgba(85, 2, 178, 0.5)',
                  transition: 'all 0.2s',
                }}
                transition="all 0.2s"
                bg={cadastro.isChecked ? 'blue.50' : ''}
                cursor={cadastro.isDisable ? 'not-allowed' : 'pointer'}
                pt="16px"
                pl={['10px', '10px', '16px']}
                pr={['10px', '10px', '16px']}
                pb="16px"
                w="full"
                onClick={() => {
                  if (!cadastro.isDisable) {
                    handleSelecionarCadastro(index);
                  }
                }}
                justifyContent="flex-start"
              >
                <Box mr="9.5px">
                  <Icon boxSize="20px" color="primary.50" as={BsArrowRight} />
                </Box>
                <Box>
                  <Flex alignItems="baseline">
                    <Box mr="10px">
                      <Text
                        color="primary.50"
                        fontSize="18px"
                        fontWeight="semibold"
                      >
                        {cadastro.title}
                      </Text>
                    </Box>
                  </Flex>
                  <Text letterSpacing="0px" color="gray.700" fontSize="14px">
                    {cadastro.descricao}
                  </Text>
                </Box>
              </Flex>
            ))}
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer mb={['20px', '0', '0']} handleAvancar={handleSubmit} />
    </>
  );
};
