import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';
import {
  buscarPromocaoAtiva,
  getCanalVendasTray,
  obterEtapaAtual,
} from 'services/tray';

import { telasExibicaoPromocao } from 'pages/Promocao/Formulario/constants';

import { obterDadosIntegracaoTray } from 'api/Tray/ObterDadosIntegração';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumIdentificacaoIntegracao } from 'constants/enum/enumIdentificacaoIntegracao';
import enumMesesDoAno from 'constants/enum/enumMesesDoAno';
import { enumTabsConfiguracaoTray } from 'constants/enum/enumTabsConfiguracaoTray';
import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';

import { Configuracoes } from './types';

type TrayPainelControleProps = {
  nomeCanalVenda: string;
  dadosTray: DadosTrayProps;
  getTray: () => Promise<void>;
  setIsLoading: (value: boolean) => void;
  isLoading: boolean;
  obterTotalizadorVendasMesAtual: () => Promise<Totalizadores | null>;
  objMesAtual?: {
    label: string;
    value: number;
  };
  isLoadingTotalizadores: boolean;
  avisoProcessoEmAndamento: {
    textoAviso: string;
    exibirAviso: boolean;
    exibirNaTab: number | null;
  };
  promocaoAtiva: PromocaoProps | null;
};

export type DadosTrayProps = {
  id: string;
  dataAtivacao: Date;
  ativo: boolean;
  sincronizacaoHabilitada: boolean;
  configuracoes: string;
};

type TrayPainelControleProviderProps = {
  children: ReactNode;
};

export const TrayPainelControleContext = createContext(
  {} as TrayPainelControleProps
);

type Totalizadores = {
  descricaoProdutoMaisVendido: string;
  faturamento: number;
  ticketMedio: number;
  quantidadeVendas: number;
};

type PromocaoProps = {
  id: string;
  nome: string;
  periodoVigencia: {
    periodoInicio: string;
    periodoFim: string;
  };
};

export const TrayPainelControleProvider = ({
  children,
}: TrayPainelControleProviderProps) => {
  const [nomeCanalVenda, setNomeCanalVenda] = useState('');
  const [dadosTray, setDadosTray] = useState<DadosTrayProps>({
    sincronizacaoHabilitada: true,
  } as DadosTrayProps);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTotalizadores, setIsLoadingTotalizadores] = useState(false);
  const [avisoProcessoEmAndamento, setAvisoProcessoEmAndamento] = useState<{
    textoAviso: string;
    exibirAviso: boolean;
    exibirNaTab: number | null;
  }>({
    textoAviso: '',
    exibirAviso: false,
    exibirNaTab: null,
  });
  const [promocaoAtiva, setPromocaoAtiva] = useState<PromocaoProps | null>(
    null
  );
  const history = useHistory();

  const mesAtual = new Date().getMonth() + 1;
  const objMesAtual = enumMesesDoAno.properties.find(
    (mes) => mes?.value === Number(mesAtual || 0)
  );

  const possuiServicoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const buscarPromocoesIntegracao = useCallback(async () => {
    const response = await api.get<void, ResponseApi<PromocaoProps[]>>(
      `${ConstanteEnderecoWebservice.LISTAR_SELECT_PROMOCAO}?telaUsoPromocao=${telasExibicaoPromocao.TRAY}`
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response.sucesso && response.dados) {
        const listaPromocoes = response.dados.map((promocao) => {
          return {
            nome: promocao.nome,
            id: promocao.id,
            periodoVigencia: {
              periodoInicio: promocao?.periodoVigencia?.periodoInicio,
              periodoFim: promocao?.periodoVigencia?.periodoFim,
            },
          };
        });
        return listaPromocoes;
      }
    }
    return [];
  }, []);

  const buscarInformacoesPromocaoAtiva = useCallback(async () => {
    const listaPromocoesNaTray = await buscarPromocoesIntegracao();
    const idPromocaoAtivaNaTray = await buscarPromocaoAtiva();

    const promocaoAtivaNoMomento = listaPromocoesNaTray?.find(
      (promocao) => promocao.id === idPromocaoAtivaNaTray
    );

    setPromocaoAtiva(
      promocaoAtivaNoMomento?.id ? promocaoAtivaNoMomento : null
    );
  }, [buscarPromocoesIntegracao]);

  const checarAvisosProcessosEmAndamento = useCallback(
    (dados: DadosTrayProps) => {
      if (dados && dados.configuracoes) {
        try {
          const configuracoesIntegracao: Configuracoes = JSON.parse(
            dados.configuracoes.replace(/\\/g, '')
          );
          const possuiAviso =
            configuracoesIntegracao.AtualizacaoTabelaPreco ||
            configuracoesIntegracao.AtualizacaoPromocao;

          const tabAtualizando = configuracoesIntegracao.AtualizacaoTabelaPreco
            ? enumTabsConfiguracaoTray.TABELA_PRECO
            : enumTabsConfiguracaoTray.PROMOCAO;

          const processoEmAndamento =
            configuracoesIntegracao.AtualizacaoTabelaPreco
              ? 'tabela de preços'
              : 'promoção';

          setAvisoProcessoEmAndamento({
            textoAviso: possuiAviso
              ? `Atualizando a ${processoEmAndamento}. Enviaremos uma notificação assim que o processo estiver concluído.`
              : '',
            exibirAviso: possuiAviso,
            exibirNaTab: possuiAviso ? tabAtualizando : null,
          });
          return;
        } catch (error) {
          setAvisoProcessoEmAndamento({
            textoAviso: '',
            exibirAviso: false,
            exibirNaTab: null,
          });
        }
      } else {
        setAvisoProcessoEmAndamento({
          textoAviso: '',
          exibirAviso: false,
          exibirNaTab: null,
        });
      }
    },
    []
  );

  const buscarNomeCanalVenda = useCallback(async () => {
    const response = await getCanalVendasTray<{ nomeCanalVenda: string }>();

    if (response !== null) {
      setNomeCanalVenda(response.nomeCanalVenda);
    }
  }, []);

  const obterEtapaTray = useCallback(async () => {
    if (possuiServicoTray) {
      const etapaAtual = await obterEtapaAtual();
      const etapasIntegracaoNaoFinalizadas =
        etapaAtual !== ETAPA_GUIA_INTEGRACAO_TRAY.ETAPA_FINALIZADA;

      if ((etapaAtual || etapaAtual === 0) && etapasIntegracaoNaoFinalizadas)
        history.push(ConstanteRotasAlternativas.TRAY_ETAPAS);

      return;
    }

    if (!dadosTray) {
      history.push(ConstanteRotas.INTEGRACAO_TRAY_TELA_COMERCIAL);
      return;
    }

    history.push(ConstanteRotas.INTEGRACAO_TRAY_DETALHES);
  }, [dadosTray, history, possuiServicoTray]);

  const obterDados = useCallback(async () => {
    setIsLoading(true);
    const response = await obterDadosIntegracaoTray();

    if (response?.sucesso) {
      await buscarInformacoesPromocaoAtiva();
      checarAvisosProcessosEmAndamento(response.dados);
      setDadosTray(response.dados);
    }
    setIsLoading(false);
  }, [buscarInformacoesPromocaoAtiva, checarAvisosProcessosEmAndamento]);

  const obterTotalizadorVendasMesAtual = useCallback(async () => {
    setIsLoadingTotalizadores(true);
    const response = await api.get<void, ResponseApi<Totalizadores>>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_TOTAL_VENDAS,
      {
        params: {
          identificacaoIntegracao: enumIdentificacaoIntegracao.TRAY,
          mes: objMesAtual?.value,
        },
      }
    );

    if (response) {
      if (response.sucesso) {
        setIsLoadingTotalizadores(false);
        return response.dados;
      }
    }
    setIsLoadingTotalizadores(false);
    return null;
  }, [objMesAtual?.value, setIsLoadingTotalizadores]);

  useEffect(() => {
    buscarNomeCanalVenda();
  }, [buscarNomeCanalVenda]);

  useEffect(() => {
    obterEtapaTray();
  }, [obterEtapaTray]);

  useEffect(() => {
    obterDados();
  }, [obterDados]);

  return (
    <TrayPainelControleContext.Provider
      value={{
        nomeCanalVenda,
        dadosTray,
        getTray: obterDados,
        setIsLoading,
        isLoading,
        obterTotalizadorVendasMesAtual,
        isLoadingTotalizadores,
        objMesAtual,
        avisoProcessoEmAndamento,
        promocaoAtiva,
      }}
    >
      {children}
    </TrayPainelControleContext.Provider>
  );
};

export function useTrayPainelControleContext(): TrayPainelControleProps {
  const context = useContext(TrayPainelControleContext);

  if (!context)
    throw new Error(
      'useTrayPainelControleContext must be used within a TrayPainelControleProvider.'
    );

  return context;
}
