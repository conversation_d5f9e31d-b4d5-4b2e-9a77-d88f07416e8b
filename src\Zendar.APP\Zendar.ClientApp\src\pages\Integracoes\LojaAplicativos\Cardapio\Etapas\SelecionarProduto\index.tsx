import { Text, VStack, Box, useMediaQuery } from '@chakra-ui/react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  finalizarIntegracao,
  salvarCategoriasSelecionadasCardapio,
} from 'services/cardapio';

import { useCardapioCategoriasStore } from 'store/Cardapio/CategoriaProdutos/CardapioCategorias.store';
import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { IconeEtapaSelecaoProdutos } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas from 'constants/rotas';

import { ModalConfirmarProdutos } from '../components/ModalConfirmarProdutos';
import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { CategoriaProduto } from './components/CategoriaProduto';

export const SelecionarProdutos = () => {
  const { setIsLoading, setPassoAtual } = useEtapasContext();
  const { categorias, obterQuantidadeProdutosSelecionados } =
    useCardapioCategoriasStore();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const history = useHistory();

  const handleAvancar = async () => {
    setIsLoading(true);
    const quantidadeProdutosSelecionados =
      obterQuantidadeProdutosSelecionados();

    if (quantidadeProdutosSelecionados === 0) {
      toast.warning('É necessário selecionar ao menos um produto.');
      setIsLoading(false);
      return;
    }

    try {
      const salvar = await ModalConfirmarProdutos({
        quantidadeProdutosSelecionados,
        todosProdutosSistema: false,
      });

      if (salvar) {
        const sucesso = await salvarCategoriasSelecionadasCardapio(categorias);
        if (sucesso) {
          const finalizado = await finalizarIntegracao();
          if (finalizado) {
            toast.success('Integração finalizada com sucesso!');
            history.push(ConstanteRotas.CARDAPIO_VENDAS_PAINEL);
          }
          setIsLoading(false);
          return;
        }
      }
    } catch (error) {
      setIsLoading(false);
    }

    setIsLoading(false);
  };

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.PRODUTOS);
  };

  return (
    <>
      {!isLargerThan700 && (
        <Header
          title="Selecionar produtos"
          handleVoltar={handleVoltar}
          icon={IconeEtapaSelecaoProdutos}
        />
      )}
      <ContainerIntegracaoFixo>
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="full"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'center'}
              letterSpacing="0px"
            >
              Selecione as categorias que serão exibidas no aplicativo. Para
              personalizar a ordem de exibição, clique no item e arraste até a
              posição desejada.
            </Text>
          </VStack>
        </Box>
        <Box w="full" maxW="1040px">
          <CategoriaProduto />
        </Box>
        <Footer my="32px" handleAvancar={handleAvancar} />
      </ContainerIntegracaoFixo>
    </>
  );
};
