import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import api, { ResponseApi } from 'services/api';
import { obterEtapaAtual } from 'services/cardapio';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import enumMesesDoAno from 'constants/enum/enumMesesDoAno';
import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';

export type ConfiguracaoDelivery = {
  configuracoes: string;
  dataAtivacao: string;
  dataHoraCadastro: string;
  dataHoraAtualizacao: string;
  id: string;
  usuarioId: string | null;
  vendedorId: string | null;
  localEstoqueId: string;
  tabelaPrecoId: string;
  promocaoId: string | null;
  identificacaoIntegracao: number;
  sincronizacaoHabilitada: boolean;
  ativo: boolean;
};

export type ConfiguracoesDetalhadas = {
  Logo: string;
  CorPersonalizada: string;
  ImagemCapa: {
    Web: string;
    Mobile: string;
  };
  Perfil: {
    Nome: string;
    Telefone: string;
    Email: string;
    Descricao: string;
    Endereco: {
      Endereco: string;
      Cep: string;
      Logradouro: string;
      Numero: string;
      Complemento: string;
      Bairro: string;
      Cidade: string;
      Estado: string;
      Pais: string;
      Latitude: number;
      Longitude: number;
    };
  };
  TempoPreparo: {
    Minimo: string;
    Maximo: string;
  };
  IdentificacaoEtapasFomerDelivery: string;
};

export const usePainelCardapio = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [dadosIntegracao, setDadosIntegracao] =
    useState<ConfiguracaoDelivery>();
  const [nomeLoja, setNomeLoja] = useState('');

  const history = useHistory();
  const formMethods = useForm();
  const { setValue } = formMethods;

  const mesAtualNumero = new Date().getMonth();

  const mesAtual = enumMesesDoAno.properties.find(
    (mes) => mes?.value === Number(mesAtualNumero + 1 || 0)
  );

  function handleVoltarPainelAdm() {
    history.push(ConstanteRotas.CARDAPIO_VENDAS_PAINEL);
  }

  const obterConfiguracoesCardapio = async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<ConfiguracaoDelivery>>(
      ConstanteEnderecoWebservice.INTEGRACAO_DELIVERY_OBTER_INTEGRACAO
    );

    if (response) {
      if (response.sucesso && response.dados) {
        if (response.dados.dataAtivacao && response.dados.ativo) {
          const sincronizacaoHabilitada =
            response.dados?.sincronizacaoHabilitada;
          const configuracoes: ConfiguracoesDetalhadas = JSON.parse(
            response?.dados?.configuracoes
          );

          if (configuracoes) {
            const { Perfil } = configuracoes;
            if (Perfil?.Nome) {
              setNomeLoja(Perfil.Nome);
            }
          }

          setDadosIntegracao(response.dados);
          setValue('lojaAberta', sincronizacaoHabilitada ?? false);
          setIsLoading(false);
        }
        const configEtapas = await obterEtapaAtual();

        if (configEtapas !== ETAPA_GUIA_INTEGRACAO_CARDAPIO.FINALIZADO) {
          setIsLoading(false);
          history.push(ConstanteRotasAlternativas.CARDAPIO_ETAPA);
        }
      }
      if (!response.sucesso) {
        history.push(ConstanteRotasAlternativas.CARDAPIO_ETAPA);
        setIsLoading(false);
        return;
      }
    }
    setIsLoading(false);
  };

  useEffect(() => {
    obterConfiguracoesCardapio();
  }, []);

  return {
    handleVoltarPainelAdm,
    mesAtual,
    isLoading,
    dadosIntegracao,
    nomeLoja,
    formMethods,
  };
};
