import { Box, Flex } from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import {
  AutoSizer,
  CellMeasurer,
  CellMeasurerCache,
  CellMeasurerCacheParams,
  List,
  ListProps,
  ScrollParams,
} from 'react-virtualized';

type VirtualizedListProps = Omit<ListProps, 'width' | 'height'> & {
  loadMore: (shouldLoad: boolean) => void;
  cellMeasurerCacheParams?: CellMeasurerCacheParams;
  showTopGradient?: boolean;
  showBottomGradient?: boolean;
};

export const VirtualizedList = ({
  onScroll,
  loadMore,
  rowRenderer,
  rowCount,
  rowHeight,
  cellMeasurerCacheParams = {
    fixedWidth: true,
    defaultHeight: 52,
  },
  colorBgGradient = 'gray.100',
  showTopGradient = true,
  showBottomGradient = true,
  noRowsRenderer = () => (
    <Flex
      color="black"
      flexDirection={['column', 'row', 'row']}
      justifyContent="space-between"
      alignItems={['left', 'center', 'center']}
      mb="5px"
      h="48px"
      bg="white"
      p="10px"
      pl={['2px', '20px', '20px']}
      pr={['10px', '20px', '20px']}
      borderRadius="6px"
    >
      Nenhum item foi encontrado.
    </Flex>
  ),
  ...props
}: VirtualizedListProps) => {
  const cache = new CellMeasurerCache(cellMeasurerCacheParams);

  const [firstItemIsVisible, setFirstItemIsVisible] = useState(true);
  const [lastItemIsVisible, setLastItemIsVisible] = useState(false);
  const listRef = useRef<List | null>(null);
  const prevRowCountRef = useRef<number>(rowCount);
  const isAutoLoadingRef = useRef(false);

  function getScrollEl(): HTMLElement | null {
    const list = listRef.current as any;
    const el = list?.Grid?._scrollingContainer as HTMLElement | undefined;
    return (
      el ??
      (document.querySelector('.ReactVirtualized__Grid') as HTMLElement | null)
    );
  }

  function temScroll(el: HTMLElement | null) {
    if (!el) return false;
    const EPS = 1;
    return el.scrollHeight - el.clientHeight > EPS;
  }

  const getVisibleItems = (e: ScrollParams) => {
    const lastItemIndex = rowCount - 1;

    const firstItemHeight = cache.rowHeight({ index: 0 });
    const lastItemHeight = cache.rowHeight({ index: lastItemIndex });
    const totalHeight = Math.round(e.scrollTop) + e.clientHeight; // Distância do scroll em relação ao topo + a altura do componente de lista virtualizada

    setFirstItemIsVisible(e.scrollTop < firstItemHeight);
    setLastItemIsVisible(totalHeight >= e.scrollHeight - lastItemHeight);
  };

  useEffect(() => {
    const prev = prevRowCountRef.current;
    prevRowCountRef.current = rowCount;

    const diminuiu = rowCount < prev;
    if (!diminuiu) return;
    if (isAutoLoadingRef.current) return;

    requestAnimationFrame(() => {
      const el = getScrollEl();
      const precisa = !temScroll(el);
      if (!precisa) return;

      isAutoLoadingRef.current = true;

      loadMore(true);
      setTimeout(() => (isAutoLoadingRef.current = false), 0);
    });
  }, [rowCount, loadMore]);

  return (
    <Box position="relative" w="full" h="full" p="1px">
      {!firstItemIsVisible && showTopGradient && (
        <Box
          position="absolute"
          h="50px"
          w="full"
          zIndex="1"
          top={0}
          left={0}
          right={0}
          bgGradient={`linear(to-b, ${colorBgGradient} 0%, rgba(0, 0, 0, 0) 100%)`}
        />
      )}
      <AutoSizer>
        {({ width, height }) => (
          <List
            ref={listRef}
            {...props}
            noRowsRenderer={noRowsRenderer}
            rowRenderer={(rowProps) => (
              <CellMeasurer
                cache={cache}
                key={rowProps.key}
                columnIndex={rowProps.columnIndex}
                rowIndex={rowProps.index}
                parent={rowProps.parent}
              >
                {rowRenderer(rowProps)}
              </CellMeasurer>
            )}
            width={width}
            height={height}
            rowCount={rowCount}
            rowHeight={cache.rowHeight}
            deferredMeasurementCache={cache}
            onScroll={(e) => {
              const isLastItemVisible =
                Math.round(e.scrollTop) >= e.scrollHeight - e.clientHeight;

              getVisibleItems(e);
              loadMore(isLastItemVisible);
              onScroll?.(e);
            }}
          />
        )}
      </AutoSizer>
      {!lastItemIsVisible && showBottomGradient && (
        <Box
          position="absolute"
          h="50px"
          w="full"
          bgGradient={`linear(to-t, ${colorBgGradient} 0%, rgba(0, 0, 0, 0) 100%)`}
          zIndex="1"
          bottom={0}
          left={0}
          right={0}
        />
      )}
    </Box>
  );
};
