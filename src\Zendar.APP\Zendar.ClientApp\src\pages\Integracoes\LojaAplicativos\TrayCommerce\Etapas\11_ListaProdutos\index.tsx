import { Box, Flex, Icon, Text, Checkbox } from '@chakra-ui/react';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import {
  useImperativeHandle,
  useCallback,
  useState,
  useEffect,
  useRef,
  useMemo,
} from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiChevronLeft } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { ListRowProps } from 'react-virtualized';

import { shallowEqual } from 'helpers/validation/shallowEqual';

import api, { ResponseApi } from 'services/api';

import { useTrayEtapasContext } from 'store/Tray';

import Loading from 'components/Layout/Loading/LoadingPadrao';
import { SearchInput } from 'components/update/Input/SearchInput';
import { VirtualizedList } from 'components/v2/ui/VirtualizedList';

import {
  ProdutoSemCadastroTrayProps,
  obterListaProdutosSemCadastroTray,
} from 'api/Integrações/Tray';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumPossuiVariacao } from 'constants/enum/enumPossuiVariacao';
import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { LeitorCodigoBarrasIcon, ListaProdutosIcon } from 'icons';

import { DrawerBuscaAvancada } from '../../PainelAdministradorTray/ProdutosTray/DrawerBuscaAvancada';
import {
  FiltrosProps,
  defaultValue,
} from '../../PainelAdministradorTray/ProdutosTray/validationForms';
import { ModalProdutosSelecionados } from '../10_BuscarProdutos/ModalProdutosSelecionados';
import { LISTA_PRODUTOS_TRAY_STORAGE_KEY } from '../produtos';

import { usePersistentProductState } from './hooks/usePersistentProductState';
import { ListaProdutoItem } from './ListaProdutoItem';
import { ModalAtencaoLimiteProdutos } from './ModalAjuda';
import { ModalEnviarProdutos } from './ModalEnviarProdutos';
import { ModalSelecionarVariacao } from './ModalSelecionarVariacao';

const TAMANHO_PAGINA = 25;

export const ProdutoSistema = () => {
  const filtros = useRef<FiltrosProps>(defaultValue);

  const {
    produtosSelecionados: listaProdutosSelecionados,
    produtosSalvos: produtosSelecionadosSalvos,
    alternarSelecaoProduto,
    atualizarVariacoesProduto,
    limparProdutosSelecionados,
    salvarProdutosSelecionados,
    deveIncluirProdutoNaListagem,
    quantidadeProdutosSelecionados,
    todosProdutosEstaoSelecionados,
    checaProdutoEstaSelecionado,
  } = usePersistentProductState();

  const [isExporting, setIsExporting] = useState(false);
  const queryClient = useQueryClient();

  const {
    ref,
    setActiveStep,
    isOpenBuscaAvancada,
    setIsOpenBuscaAvancada,
    setTemProdutoSelecionadoListagem,
    setHasFiltros,
    setIsProdutoSistema,
    limparProdutosSalvos,
  } = useTrayEtapasContext();

  const formMethods = useForm();

  const {
    formState: { isDirty },
    handleSubmit,
  } = formMethods;

  const jaExibiuModalAjuda = useRef(false);

  function pegarIdsVariacoes(item: ProdutoSemCadastroTrayProps): string[] {
    if (item.tipoProduto !== enumPossuiVariacao.VARIACAO) return [];
    return (item.produtoCores ?? [])
      .flatMap((cor) => cor.produtoCorTamanhos ?? [])
      .map((ct) => ct.produtoCorTamanho.produtoCorTamanhoId);
  }

  const transformarProdutoParaLista = useCallback(
    (item: ProdutoSemCadastroTrayProps): ProdutoSemCadastroTrayProps => {
      return {
        ...item,
        listaProdutoCorTamanhoId: pegarIdsVariacoes(item),
        estaSelecionado: checaProdutoEstaSelecionado(item.id),
      };
    },
    [checaProdutoEstaSelecionado]
  );

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isFetching,
    isRefetching,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['produtos-tray-sem-cadastro', filtros.current],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await obterListaProdutosSemCadastroTray({
        dadosPaginacao: {
          currentPage: pageParam,
          orderColumn: 'Nome',
          orderDirection: 'asc',
          pageSize: TAMANHO_PAGINA,
        },
        parametros: { ...filtros.current },
      });

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (!response?.sucesso || !response?.dados) {
        return {
          registros: [],
          total: 0,
          paginaAtual: pageParam,
          temProdutosValidos: false,
        };
      }

      const registros = response.dados.registros ?? [];
      const total = response.dados.total ?? 0;

      const produtosValidos = registros.filter((item: any) =>
        deveIncluirProdutoNaListagem(item, [])
      );

      return {
        registros: produtosValidos.map((item: any) =>
          transformarProdutoParaLista(item)
        ),
        total,
        paginaAtual: pageParam,
        temProdutosValidos: produtosValidos.length > 0,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const totalPaginas = Math.ceil(lastPage.total / TAMANHO_PAGINA);
      const proximaPagina = allPages.length + 1;
      return proximaPagina <= totalPaginas ? proximaPagina : undefined;
    },
    refetchOnWindowFocus: false,
    staleTime: 50000, // 50 segundos
  });

  const listaProdutos = useMemo(() => {
    const todosProdutos = data?.pages?.flatMap((page) => page.registros) ?? [];

    const produtosNaoSalvos = todosProdutos.filter((produto) =>
      deveIncluirProdutoNaListagem(produto, [])
    );

    return produtosNaoSalvos.map((produto) => ({
      ...produto,
      estaSelecionado: checaProdutoEstaSelecionado(produto.id),
    }));
  }, [data, checaProdutoEstaSelecionado, deveIncluirProdutoNaListagem]);

  const todosProdutosEstaoSelecionadosNaLista = useMemo(
    () => todosProdutosEstaoSelecionados(listaProdutos),
    [listaProdutos, todosProdutosEstaoSelecionados]
  );

  const recarregarLista = useCallback(() => {
    refetch();
  }, [refetch]);

  const carregarMaisProdutos = useCallback(async () => {
    const MINIMO_ITENS_PARA_SCROLL = 15;

    const temPoucosItens = listaProdutos.length < MINIMO_ITENS_PARA_SCROLL;
    const listaVazia = listaProdutos.length === 0;
    const temMaisPaginasDisponiveis = hasNextPage;
    const deveBuscarMais =
      (temPoucosItens || listaVazia) && temMaisPaginasDisponiveis;

    if (deveBuscarMais && !isFetchingNextPage) {
      try {
        await fetchNextPage();
      } catch (error) {
        toast.warning('Não foi possível carregar mais produtos');
      }
    }
  }, [listaProdutos.length, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleCarregarMaisProdutos = useCallback(
    (isLastItemVisible: boolean, forceLoad?: boolean) => {
      if (
        (isLastItemVisible || forceLoad) &&
        hasNextPage &&
        !isFetchingNextPage
      ) {
        fetchNextPage();
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  const handleLimparSelecao = useCallback(() => {
    limparProdutosSelecionados();
  }, [limparProdutosSelecionados]);

  useEffect(() => {
    if (!isLoading && !isFetchingNextPage && data) {
      carregarMaisProdutos();
    }
  }, [isLoading, isFetchingNextPage, data, carregarMaisProdutos]);

  const filtersSubmit = useCallback(
    (filters: FiltrosProps) => {
      filtros.current = filters;
      recarregarLista();
    },
    [recarregarLista]
  );

  const handleVoltar = useCallback(() => {
    limparProdutosSalvos();
    queryClient.removeQueries({ queryKey: ['produtos-tray-sem-cadastro'] });
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.TIPO_CADASTRO);
  }, [setActiveStep, limparProdutosSalvos, queryClient]);

  const handleSalvarListaProdutosSelecionados = useCallback(() => {
    const quantidadeSalvos = listaProdutosSelecionados.length;

    salvarProdutosSelecionados();
    handleLimparSelecao();

    toast.success(`${quantidadeSalvos} produto(s) selecionado(s) com sucesso`);
  }, [
    salvarProdutosSelecionados,
    handleLimparSelecao,
    listaProdutosSelecionados.length,
  ]);

  const handleExportarProduto = useCallback(
    async (notificar: boolean) => {
      setIsExporting(true);
      const response = await api.put<void, ResponseApi<boolean>>(
        ConstanteEnderecoWebservice.INTEGRACOES_TRAY_EXPORTAR_PRODUTO,
        {
          listaProduto: produtosSelecionadosSalvos?.map((item) => ({
            produtoId: item.produtoId,
            listaProdutoCorTamanhoId: item.listaProdutoCorTamanhoId,
          })),
          notificar,
        }
      );
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }

        if (response.sucesso) {
          localStorage.removeItem(LISTA_PRODUTOS_TRAY_STORAGE_KEY);
          if (notificar) {
            setIsProdutoSistema(true);
            setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.ENVIANDO_ZENDAR);
          } else {
            handleSalvarListaProdutosSelecionados();
          }
          setIsExporting(false);
        }
        setIsExporting(false);
      }

      setIsExporting(false);
    },
    [
      handleSalvarListaProdutosSelecionados,
      produtosSelecionadosSalvos,
      setActiveStep,
      setIsProdutoSistema,
    ]
  );

  const handleAbrirVariacoes = useCallback(
    async (produto: ProdutoSemCadastroTrayProps) => {
      const variacoes = listaProdutosSelecionados.find(
        (item) => item.produtoId === produto.id
      );

      const variacoesSelecionadas = await ModalSelecionarVariacao({
        id: produto.id,
        nomeProduto: produto.nome,
        dataVariacoes: variacoes?.listaProdutoCorTamanhoId,
      });

      if (variacoesSelecionadas && variacoesSelecionadas.length > 0) {
        atualizarVariacoesProduto(produto.id, variacoesSelecionadas);
      }
    },
    [listaProdutosSelecionados, atualizarVariacoesProduto]
  );

  const handleToggleSelecionarProduto = (item: ProdutoSemCadastroTrayProps) => {
    alternarSelecaoProduto(item);
  };

  function handleToggleSelecionarTodosProdutos() {
    const todosEstaoSelecionados = todosProdutosEstaoSelecionadosNaLista;

    if (!todosEstaoSelecionados) {
      listaProdutos.forEach((produto) => {
        if (!checaProdutoEstaSelecionado(produto.id)) {
          alternarSelecaoProduto(produto);
        }
      });
    } else {
      listaProdutos.forEach((produto) => {
        if (checaProdutoEstaSelecionado(produto.id)) {
          alternarSelecaoProduto(produto);
        }
      });
    }
  }

  const handleReset = handleSubmit((formData) => {
    const filtersIsDirty = !shallowEqual(formData, filtros || {});

    if (filtersIsDirty) {
      filtros.current = {
        ...filtros.current,
        nomeReferencia: formData?.nomeReferencia || '',
      };
      recarregarLista();
    }
  });

  const handleEnviarProdutos = () => {
    handleExportarProduto(true);
  };

  useImperativeHandle(ref, () => ({
    handleConfirmarProdutoSelecionado: () =>
      handleSalvarListaProdutosSelecionados(),
    handleAvancar: () => {
      ModalEnviarProdutos({
        amountProdutos: produtosSelecionadosSalvos.length,
        handleEnviarProdutos: () => handleEnviarProdutos(),
        listProdutos: produtosSelecionadosSalvos,
        reload: recarregarLista,
      });
    },
    handleExibirProdutosSalvos: () => {
      ModalProdutosSelecionados({
        listProdutos: produtosSelecionadosSalvos,
        reload: recarregarLista,
      });
    },
  }));

  useEffect(() => {
    setHasFiltros(isDirty);
  }, [isDirty, setHasFiltros]);

  useEffect(() => {
    setTemProdutoSelecionadoListagem(quantidadeProdutosSelecionados > 0);
  }, [quantidadeProdutosSelecionados, setTemProdutoSelecionadoListagem]);

  useEffect(() => {
    if (jaExibiuModalAjuda.current) return;
    ModalAtencaoLimiteProdutos();
    jaExibiuModalAjuda.current = true;
  }, []);

  return (
    <Flex h="full" w="full">
      <Box
        h="full"
        onClick={handleVoltar}
        cursor="pointer"
        mt="8px"
        ml="-32px"
        mr="32px"
      >
        <Icon
          color="black"
          display={['none', 'none', 'flex']}
          boxSize="30px"
          onClick={handleVoltar}
          cursor="pointer"
          as={FiChevronLeft}
        />
      </Box>
      <Flex h="full" flexDirection="column" w="full">
        <FormProvider {...formMethods}>
          {(isLoading || isFetching || isRefetching || isExporting) && (
            <Loading />
          )}

          <Flex
            mb="24px"
            borderBottomWidth="1px"
            borderColor="gray.200"
            justifyContent="space-between"
            w="full"
            alignItems="center"
            pb="6px"
          >
            {quantidadeProdutosSelecionados > 0 ? (
              <Flex
                flexDirection={['column', 'row']}
                justifyContent="center"
                alignItems="baseline"
              >
                <Flex justifyContent="center" alignItems="baseline">
                  <Box ml={['', '20px']} mr={['10px', '10px', '20px']}>
                    <Checkbox
                      onChange={handleToggleSelecionarTodosProdutos}
                      isChecked={todosProdutosEstaoSelecionadosNaLista}
                      colorScheme="primary"
                    />
                  </Box>
                  <Text
                    textAlign="center"
                    fontSize={['20px', '20px', '28px']}
                    fontWeight="bold"
                    whiteSpace="nowrap"
                  >
                    {quantidadeProdutosSelecionados === 1
                      ? `${quantidadeProdutosSelecionados} item selecionado`
                      : `${quantidadeProdutosSelecionados} itens selecionados`}
                  </Text>
                </Flex>
                <Text
                  textAlign="center"
                  fontSize="14px"
                  ml="24px"
                  mb={['10px', '']}
                  cursor="pointer"
                  color="red.500"
                  onClick={handleLimparSelecao}
                  textDecoration="underline"
                  whiteSpace="nowrap"
                >
                  Limpar seleção
                </Text>
              </Flex>
            ) : (
              <Flex
                gap="12px"
                alignItems="center"
                justifyContent="center"
                ml="12px"
              >
                <Icon
                  boxSize="40px"
                  size={40}
                  color="primary.50"
                  as={ListaProdutosIcon}
                />
                <Text
                  textAlign="center"
                  fontSize={['20px', '20px', '28px']}
                  fontWeight="bold"
                  whiteSpace="nowrap"
                >
                  Lista de produtos
                </Text>
              </Flex>
            )}
          </Flex>
          <Flex w={['100%', '100%', '80%']} mb="16px">
            <SearchInput
              exibirTrailingIcon
              trailingIcon={LeitorCodigoBarrasIcon}
              type="search"
              placeholder="Buscar o produto por Nome ou Referência"
              onEnterKeyPress={() => handleReset()}
              isDisabled={isLoading}
              id="nomeReferencia"
              name="nomeReferencia"
              height="40px"
            />
          </Flex>
          <Flex flexGrow={1}>
            <Box minW="200px" height="full" w="full">
              <VirtualizedList
                rowCount={listaProdutos.length}
                loadMore={(isLastItemVisible: boolean, forceLoad?: boolean) => {
                  handleCarregarMaisProdutos(isLastItemVisible, forceLoad);
                }}
                colorBgGradient="gray.50"
                rowRenderer={({ key, style, index }: ListRowProps) => {
                  const produto = listaProdutos[index];

                  return (
                    <Box style={style} key={key} px={1}>
                      {index === 0 && <Box h="4px" />}
                      <ListaProdutoItem
                        produto={produto}
                        handleAbrirVariacoes={handleAbrirVariacoes}
                        handleAlternarSelecionarProduto={
                          handleToggleSelecionarProduto
                        }
                      />
                    </Box>
                  );
                }}
              />
            </Box>
          </Flex>
          <DrawerBuscaAvancada
            currentFilters={filtros.current}
            filtersSubmit={filtersSubmit}
            filtrosReset={defaultValue}
            isOpen={isOpenBuscaAvancada}
            onClose={() => setIsOpenBuscaAvancada(false)}
          />
        </FormProvider>
      </Flex>
    </Flex>
  );
};
