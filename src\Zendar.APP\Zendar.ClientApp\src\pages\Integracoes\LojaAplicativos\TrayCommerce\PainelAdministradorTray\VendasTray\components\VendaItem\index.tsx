import { Box, Checkbox, Td, Tr, Text, Flex, Icon } from '@chakra-ui/react';
import { useCallback } from 'react';
import { toast } from 'react-toastify';

import { moneyMask } from 'helpers/format/fieldsMasks';
import {
  formatDate,
  formatDateHourMinute,
} from 'helpers/format/formatStringDate';

import api, { ResponseApi } from 'services/api';
import {
  abrirModalConfirmacaoImportarPedidoNovamente,
  reprocessarPedido,
} from 'services/tray';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import {
  ActionMenuItem,
  ActionsMenu,
} from 'components/update/Table/ActionsMenu';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumIdentificacaoIntegracao } from 'constants/enum/enumIdentificacaoIntegracao';
import StatusFiscaisEnum from 'constants/enum/fiscal/statusFiscal';
import IdentificacaoTipoOperacaoEnum from 'constants/enum/identificacaoTipoOperacao';
import StatusOperacaoEnum from 'constants/enum/statusOperacao';
import { AvisoIcon, NotaFiscalEmitidaIcon } from 'icons';

import { useVenda } from '../../hooks';
import { ListVendasProps } from '../../validationForms';
import { ModalHistoricoAcoes } from '../ModaHistoricoAcoes';

import { useVendaItem } from './hooks';

type ItemComDataHora = {
  situacao: number;
  dataHora: string; // Formato ISO, ex: "2024-09-06T18:22:46.919Z"
};

type VendaItemProps = {
  handleGerarNotaFiscal: (data: ListVendasProps[]) => void;
  vendaItem: ListVendasProps;
  handleAbrirModalCancelarVenda: (vendaItem: ListVendasProps) => void;
  isSelecionarVenda: boolean;
  handleToggleSelecionarVendas: (index: number) => void;
  index: number;
  handleOpenModalCodigoRastreio(vendaItem: ListVendasProps[]): void;
  onReload: () => void;
};

export const VendaItem = ({
  handleGerarNotaFiscal,
  vendaItem,
  handleAbrirModalCancelarVenda,
  isSelecionarVenda,
  handleToggleSelecionarVendas,
  index,
  onReload,
  handleOpenModalCodigoRastreio,
}: VendaItemProps) => {
  const { casasDecimais } = usePadronizacaoContext();

  const {
    abrirModalVisualizarPendencias,
    reloadList,
    setIsLoading,
    abrirModalPendenciaNotaFiscalRejeitada,
  } = useVenda(onReload, Number(vendaItem.numeroVenda), async () =>
    handleAbrirModalCancelarVenda(vendaItem)
  );

  const {
    backgroundVenda,
    abrirModalCompartilhar,
    operacaoTipoVenda,
    vendaOuPedidoCancelado,
    movimentacoesFinanceiras,
    vendaOuPedidoPossuiPendencia,
    notaFiscalRejeitada,
    notaFiscalAutorizada,
    acaoEmitirNotaFiscal,
    operacaoSemStatus,
    acaoVerDetalheVenda,
  } = useVendaItem(vendaItem, casasDecimais, handleGerarNotaFiscal);

  const ordenarPorDataHora = (lista: ItemComDataHora[]): ItemComDataHora[] => {
    return [...lista].sort((a, b) => {
      const dataA = new Date(a.dataHora).getTime();
      const dataB = new Date(b.dataHora).getTime();
      return dataB - dataA;
    });
  };

  const possuiNotaFiscalEmitida = vendaItem.numeroNotaFiscal !== null;
  const possuiAlerta =
    !possuiNotaFiscalEmitida &&
    !vendaOuPedidoCancelado &&
    vendaItem.possuiAlertaPlataforma;

  const handleExibirHistoricoDeAcoes = useCallback(
    async (venda: ListVendasProps) => {
      const response = await api.get<
        void,
        ResponseApi<
          {
            situacao: number;
            dataHora: string;
          }[]
        >
      >(ConstanteEnderecoWebservice.INTEGRACAO_HISTORICO_ACOES_LOG, {
        params: {
          idPedidoPlataforma: venda.numeroPedido,
        },
      });
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }
        if (response.sucesso && response.dados) {
          const dados = response.dados.map((item) => ({
            situacao: item.situacao,
            dataHora: item.dataHora,
          }));
          ModalHistoricoAcoes({
            listAcoes: ordenarPorDataHora(dados),
          });
        }
      }
    },
    []
  );

  const handleReprocessarPedido = async (venda: ListVendasProps) => {
    setIsLoading(true);
    const sucesso = await reprocessarPedido(
      enumIdentificacaoIntegracao.TRAY,
      venda.integracaoPedidoId
    );
    if (sucesso) reloadList();
    setIsLoading(false);
  };

  const buscarPendencias = async (pedidoId: string) => {
    const identificacaoIntegracao = enumIdentificacaoIntegracao.TRAY;

    const rotaApi =
      ConstanteEnderecoWebservice.INTEGRACAO_OBTER_PENDENCIAS_PEDIDO?.replace(
        '{identificacaoIntegracao}',
        identificacaoIntegracao?.toString()
      )?.replace('{pedidoId}', pedidoId);

    const response = await api.get<
      void,
      ResponseApi<{
        pendencias: {
          cadastroPlataformaId: string;
          cadastroSistemaId: string;
          clienteFornecedorId: string;
          descricao: string;
          descricaoProduto: string;
          id: string;
          integracaoPedidoId: string;
          tipoCadastro: number;
          tipoPendencia: number;
          tipoProduto: number;
        }[];
      }>
    >(rotaApi);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
        return null;
      }
      if (response.sucesso && response.dados) {
        const pendencias = response.dados?.pendencias.map((item) => ({
          ...item,
          possuiPendencia: true,
        }));

        return pendencias;
      }
    }
  };

  const handleAbrirModalVisualizarPendencias = async () => {
    const pendencias = await buscarPendencias(vendaItem.integracaoPedidoId);
    if (!pendencias) return;
    abrirModalVisualizarPendencias(pendencias, vendaItem.integracaoPedidoId);
  };

  const hadleAbrirModalPendenciaNotaFiscalRejeitada = async () => {
    abrirModalPendenciaNotaFiscalRejeitada(
      vendaItem.motivoNotaFiscal || '',
      () => handleGerarNotaFiscal([vendaItem])
    );
  };

  const acaoImportaPedidoNovamente = async (pedidoId: string) => {
    await abrirModalConfirmacaoImportarPedidoNovamente({
      pedidoId,
      callback: async () => {
        setTimeout(async () => {
          window.location.reload();
        }, 200);
      },
    });
  };

  return (
    <>
      <Tr
        sx={{
          boxShadow: '0px 0px 4px #00000029',
          borderRadius: '6px',

          '& > td': {
            height: '64px',
            bg: backgroundVenda(),
          },
        }}
        cursor={isSelecionarVenda ? 'pointer' : 'default'}
        onClick={() => {
          if (isSelecionarVenda && !vendaOuPedidoCancelado) {
            handleToggleSelecionarVendas(index);
          }
        }}
      >
        {isSelecionarVenda && (
          <Td paddingRight="0">
            {!vendaOuPedidoCancelado && (
              <Checkbox
                isChecked={vendaItem.isChecked}
                mb="1px"
                onChange={() => handleToggleSelecionarVendas(index)}
                colorScheme="primary"
              />
            )}
          </Td>
        )}
        <Td>
          <Box>
            <Text
              color={vendaOuPedidoCancelado ? 'red.600' : 'black'}
              fontWeight="600"
              fontSize="14px"
            >
              {formatDate(vendaItem.dataEmissao)}
            </Text>
            <Text fontSize="12px" color="gray.700" fontWeight="300">
              {formatDateHourMinute(vendaItem.dataEmissao).slice(11)}h
            </Text>
          </Box>
        </Td>
        <Td>
          <Box>
            <Text
              color={vendaOuPedidoCancelado ? 'red.600' : 'black'}
              fontWeight="600"
              fontSize="14px"
            >
              {vendaItem.origem}
            </Text>
            <Text fontSize="12px" color="gray.700" fontWeight="300">
              {vendaItem.numeroPedido}
            </Text>
          </Box>
        </Td>
        <Td>
          <Box>
            <Text
              color={vendaOuPedidoCancelado ? 'red.600' : 'black'}
              fontWeight="600"
              fontSize="14px"
            >
              {vendaItem.cliente}
            </Text>
            <Text fontSize="12px" color="gray.700" fontWeight="300">
              {vendaItem?.apelido}
            </Text>
          </Box>
        </Td>
        <Td>
          <Flex h="full" justifyContent="flex-start" alignItems="left">
            {vendaItem.statusNotaFiscal === StatusFiscaisEnum.Autorizada && (
              <Flex
                display="grid"
                alignItems="center"
                justifyContent="center"
                h="full"
              >
                <Icon
                  cursor="pointer"
                  color="gray.700"
                  mr="5px"
                  mb="3px"
                  fontSize="30px"
                  as={NotaFiscalEmitidaIcon}
                />
              </Flex>
            )}

            <Flex
              display="grid"
              alignItems="center"
              justifyContent="center"
              h={
                vendaItem.statusNotaFiscal === StatusFiscaisEnum.Autorizada
                  ? undefined
                  : 'full'
              }
            >
              <Box>
                <Text
                  color={vendaOuPedidoCancelado ? 'white' : 'black'}
                  textAlign="left"
                  borderRadius="16px"
                  w={vendaOuPedidoCancelado ? '70px' : undefined}
                  fontSize={vendaOuPedidoCancelado ? '12px' : '14px'}
                  fontWeight={vendaOuPedidoCancelado ? '300' : '600'}
                  py={vendaOuPedidoCancelado ? '1px' : undefined}
                  px={vendaOuPedidoCancelado ? '5px' : undefined}
                  bg={vendaOuPedidoCancelado ? 'red.500' : undefined}
                >
                  {vendaOuPedidoCancelado ? 'Cancelada' : vendaItem.situacao}
                </Text>
                {vendaItem.statusNotaFiscal ===
                  StatusFiscaisEnum.Autorizada && (
                  <Text
                    textAlign="left"
                    fontSize="12px"
                    color="gray.700"
                    fontWeight="300"
                  >
                    NFe | {vendaItem.numeroNotaFiscal}
                  </Text>
                )}
              </Box>
            </Flex>
          </Flex>
        </Td>
        <Td color={vendaOuPedidoCancelado ? 'red.600' : undefined}>
          {moneyMask(vendaItem.valorTotal, true)}
        </Td>
        <Td>
          <Box>
            <Text color={vendaOuPedidoCancelado ? 'red.600' : undefined}>
              {Number(vendaItem.numeroVenda) > 0 ? (
                vendaItem.numeroVenda
              ) : (
                <></>
              )}
            </Text>
            {movimentacoesFinanceiras.length > 0 ? (
              <>
                {movimentacoesFinanceiras.length === 1 ? (
                  <Text fontSize="12px" color="gray.700" fontWeight="300">
                    {movimentacoesFinanceiras[0]?.descricaoFormaPagto} |{' '}
                    {
                      movimentacoesFinanceiras[0]?.descricaoParcelas.split(
                        'em '
                      )[1]
                    }
                  </Text>
                ) : (
                  <Text fontSize="12px" color="gray.700" fontWeight="300">
                    Ver pagamentos
                  </Text>
                )}
              </>
            ) : (
              <></>
            )}
          </Box>
        </Td>
        <Td>
          {!operacaoSemStatus && (
            <>
              {(vendaOuPedidoPossuiPendencia || notaFiscalRejeitada) &&
              !vendaOuPedidoCancelado ? (
                <Box
                  onClick={
                    !vendaOuPedidoPossuiPendencia && notaFiscalRejeitada
                      ? hadleAbrirModalPendenciaNotaFiscalRejeitada
                      : handleAbrirModalVisualizarPendencias
                  }
                >
                  <Icon
                    cursor="pointer"
                    fontSize="20px"
                    as={AvisoIcon}
                    ml="10px"
                  />
                </Box>
              ) : (
                <ActionsMenu
                  items={
                    vendaOuPedidoCancelado
                      ? [acaoVerDetalheVenda]
                      : ([
                          vendaItem.operacaoId ? acaoVerDetalheVenda : null,
                          vendaItem.reprocessarPedido
                            ? {
                                content: 'Reprocessar pedido',
                                onClick: async () => {
                                  await handleReprocessarPedido(vendaItem);
                                },
                              }
                            : null,
                          possuiAlerta && {
                            content: 'Histórico de ações',
                            onClick: () => {
                              handleExibirHistoricoDeAcoes(vendaItem);
                            },
                          },

                          vendaItem.statusNotaFiscal ===
                            StatusFiscaisEnum.Autorizada ||
                          (vendaItem.identificacaoTipoOperacao ===
                            IdentificacaoTipoOperacaoEnum.VENDA &&
                            vendaItem.status === StatusOperacaoEnum.EFETUADA)
                            ? null
                            : {
                                content: 'Importar pedido novamente',
                                onClick: async () => {
                                  await acaoImportaPedidoNovamente(
                                    vendaItem.integracaoPedidoId
                                  );
                                },
                              },
                          vendaItem.operacaoId
                            ? {
                                content: 'Compartilhar',
                                onClick: () => {
                                  abrirModalCompartilhar(vendaItem.operacaoId);
                                },
                              }
                            : null,
                          !operacaoTipoVenda && {
                            content: 'Cancelar',
                            onClick: () => {
                              handleAbrirModalCancelarVenda(vendaItem);
                            },
                          },

                          operacaoTipoVenda &&
                          notaFiscalAutorizada &&
                          vendaItem.codigoRastreioEnviado === false
                            ? {
                                content: 'Código de rastreio',
                                onClick: () =>
                                  handleOpenModalCodigoRastreio([vendaItem]),
                              }
                            : null,
                          vendaItem.operacaoId &&
                          operacaoTipoVenda &&
                          !notaFiscalAutorizada &&
                          !vendaOuPedidoCancelado
                            ? acaoEmitirNotaFiscal
                            : null,
                        ].filter(Boolean) as ActionMenuItem[])
                  }
                />
              )}
            </>
          )}
        </Td>
      </Tr>
      <Box h="5px" />
    </>
  );
};
