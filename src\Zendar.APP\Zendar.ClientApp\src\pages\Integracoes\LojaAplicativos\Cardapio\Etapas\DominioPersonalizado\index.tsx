import { Text, VStack, Box, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  atualizarIdentificadorUrl,
  obterDominioDelivery,
  obterIdentificadorUrl,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import Input from 'components/PDV/Input';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import { PagamentosLinkIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { FormData, yupResolver } from './validationForms';

export const DominioPersonalizado = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [dominioDelivery, setDominioDelivery] = useState('');

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: {
      dominio: '',
    },
  });

  const {
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = formMethods;

  const dominioPersonalizado = watch('dominio');

  const handleAvancar = handleSubmit(async ({ dominio }) => {
    setIsLoading(true);
    const response = await atualizarIdentificadorUrl(dominio);
    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.PERFIL_LOJA);
    }

    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.INICIO);
  };

  const obterDominioSalvo = useCallback(async () => {
    setIsLoading(true);
    const dominioSalvo = await obterIdentificadorUrl();
    if (dominioSalvo) {
      setValue('dominio', dominioSalvo);
    }
    setIsLoading(false);
  }, [setValue, setIsLoading]);

  const obterDominioDeliverySalvo = useCallback(async () => {
    const dominioSalvo = await obterDominioDelivery();
    setDominioDelivery(dominioSalvo);
  }, []);

  useEffect(() => {
    obterDominioSalvo();
  }, [obterDominioSalvo]);

  useEffect(() => {
    obterDominioDeliverySalvo();
  }, [obterDominioDeliverySalvo]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height="100vh">
        <Header
          title="Domínio personalizado"
          handleVoltar={handleVoltar}
          icon={PagamentosLinkIcon}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
          overflow="auto"
        >
          <VStack
            mb="24px"
            color="primary.50"
            fontSize="14px"
            spacing="24px"
            flexWrap="wrap"
          >
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Para configurar duas ou mais lojas dentro da mesma conta é
              necessário{' '}
              <Text as="span" fontWeight="bold">
                definir um domínio personalizado para cada unidade.
              </Text>{' '}
              Dessa forma cada loja terá o seu próprio link exclusivo,
              direcionando o usuário diretamente para o cardápio.
            </Text>
          </VStack>
          <VStack
            spacing="0px"
            mb="32px"
            w="full"
            justify="flex-start"
            align="flex-start"
            height="fit-content"
            padding={['16px', '18px 32px']}
            bg="gray.100"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="5px"
          >
            <Text
              color="black"
              fontSize="14px"
              letterSpacing="0px"
              fontWeight={['bold', 'normal']}
            >
              Domínio da conta
            </Text>
            <Text
              color="primary.50"
              fontSize={['14px', '18px']}
              letterSpacing="0px"
              fontWeight="normal"
            >
              {dominioDelivery}
            </Text>
          </VStack>
          <VStack spacing={errors?.dominio ? '16px' : '10px'}>
            <Input
              name="dominio"
              label="Domínio personalizado para esta loja"
              placeholder="Ex: Loja01"
              onInput={(e: React.FormEvent<HTMLInputElement>) => {
                const valorOriginal = e.currentTarget.value;

                const valorLimpo = valorOriginal.replace(/[^a-z0-9]/gi, '');

                if (valorLimpo !== valorOriginal) {
                  e.currentTarget.value = valorLimpo;
                }
              }}
            />
            <Text
              color="aquamarine.700"
              fontSize={['14px', '16px']}
              letterSpacing="0px"
              fontWeight="normal"
              textAlign="start"
              w="full"
            >
              {`${dominioDelivery}/${dominioPersonalizado?.toLocaleLowerCase()}`}
            </Text>
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
        isDisabledAvancar={!dominioPersonalizado}
      />
    </FormProvider>
  );
};
