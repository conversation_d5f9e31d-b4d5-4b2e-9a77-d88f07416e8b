import { Text, VStack, Box, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  atualizarMidiasSociais,
  MidiasSociais,
  obterMidiasSociaisSalvas,
  RedeSocial,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { IconeEtapaMidiasSociais } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { ContainerMidiasSociais } from './MidiasSociais';
import { defaultValues, yupResolver } from './validationForms';

type MidiasEntrada = Record<string, string>;

type MidiasSaida = {
  midiasSociais: MidiasSociais[];
};

export const MidiasSociaisEtapa = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<MidiasEntrada>({
    defaultValues,
    resolver: yupResolver,
  });

  const { setValue, handleSubmit } = formMethods;

  function transformarMidias(input: MidiasEntrada): MidiasSaida {
    return {
      midiasSociais: Object.entries(input)
        .filter(
          ([chave, valor]) =>
            chave !== 'exibir' &&
            typeof valor === 'string' &&
            valor.trim() !== ''
        )
        .map(([chave, valor]) => ({
          tipo: chave.toUpperCase() as RedeSocial,
          valor: valor as string,
        })),
    };
  }

  const handleAvancar = handleSubmit(async (data) => {
    setIsLoading(true);
    const midias = transformarMidias(data);
    const response = await atualizarMidiasSociais(midias);
    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.PRODUTOS);
    }
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.TABELA_PRECO);
  };

  const obterMidiasSalvas = useCallback(async () => {
    setIsLoading(true);
    const midiasSalvas = await obterMidiasSociaisSalvas();
    midiasSalvas?.forEach(({ tipo, valor }) => {
      const redeSocial = tipo?.toLowerCase();

      if (redeSocial) {
        setValue(redeSocial, valor);
      }
    });
    setIsLoading(false);
  }, []);

  useEffect(() => {
    obterMidiasSalvas();
  }, [obterMidiasSalvas]);

  return (
    <>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Contatos e Redes Sociais"
          handleVoltar={handleVoltar}
          icon={IconeEtapaMidiasSociais}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'center'}
              letterSpacing="0px"
            >
              Utilize os campos abaixo para cadastrar e exibir suas redes
              sociais. Para compartilhar o link correto das páginas, acesse o
              seu perfil, toque em compartilhar perfil, copie o link do perfil e
              cole o link nos campos abaixo.
            </Text>
          </VStack>
        </Box>
        <Box
          w="full"
          maxW="600px"
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
        >
          <FormProvider {...formMethods}>
            <ContainerMidiasSociais />
          </FormProvider>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </>
  );
};
