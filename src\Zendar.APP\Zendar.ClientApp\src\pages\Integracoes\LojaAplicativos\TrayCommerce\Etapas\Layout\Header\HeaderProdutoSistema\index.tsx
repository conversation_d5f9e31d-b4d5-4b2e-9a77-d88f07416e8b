import { Flex, Text, Icon, Button, useMediaQuery } from '@chakra-ui/react';
import { BiHelpCircle } from 'react-icons/bi';

import { useTrayEtapasContext } from 'store/Tray';

import { BuscaAvancadaButton } from 'components/update/BuscaAvancadaButton';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';

import { ModalAtencaoLimiteProdutos } from '../../../11_ListaProdutos/ModalAjuda';

export const HeaderProdutoSistema = () => {
  const {
    ref,
    activeStep,
    setIsOpenBuscaAvancada,
    produtosSelecionadosSalvos,
    hasFiltros,
  } = useTrayEtapasContext();

  const quantidadeProdutosSalvosEmStorage = produtosSelecionadosSalvos?.length;

  const jaTemProdutosSalvos = quantidadeProdutosSalvosEmStorage > 0;

  const estaEnviandoProdutosParaTray =
    activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.LISTA_PRODUTO_SISTEMA;

  const avancarEtapaDesabilitado = !!(
    !jaTemProdutosSalvos && estaEnviandoProdutosParaTray
  );

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  return (
    <>
      {jaTemProdutosSalvos && (
        <Flex
          mr={['10px', '10px', '10px', '0']}
          flex={2}
          mb={['10px', '10px', '0']}
          justifyContent="center"
          alignItems="center"
        >
          <Text
            whiteSpace="nowrap"
            onClick={() => {
              if (ref.current?.handleExibirProdutosSalvos) {
                ref.current?.handleExibirProdutosSalvos();
              }
            }}
          >
            Total de produtos selecionados:
            <span
              style={{
                color: 'black',
                textDecoration: 'underline',
                marginLeft: '5px',
              }}
            >
              {quantidadeProdutosSalvosEmStorage}
            </span>
          </Text>
        </Flex>
      )}
      {estaEnviandoProdutosParaTray && (
        <BuscaAvancadaButton
          borderRadius="full"
          bg="transparent"
          height="32px"
          colorScheme={hasFiltros ? 'blue' : 'gray'}
          variant={hasFiltros ? 'solid' : 'outlineDefault'}
          hasFilters={hasFiltros}
          setIsModalBuscaAvancadaOpen={() => {
            setIsOpenBuscaAvancada(true);
          }}
          padding="10px 24px"
          justifyContent="center"
          minW={['155px', '155px', '165px', '165px']}
          width={['full', '50%', '165px', '165px']}
          fontWeight="normal"
          fontSize="14px"
        >
          {hasFiltros ? 'Filtros selecionados' : 'Busca avançada'}
        </BuscaAvancadaButton>
      )}
      <Button
        w={['full', 'full', '120px']}
        variant="solid"
        h="32px"
        isDisabled={avancarEtapaDesabilitado}
        colorScheme={avancarEtapaDesabilitado ? 'gray' : 'purple'}
        fontWeight="normal"
        color="white"
        onClick={() => {
          if (ref.current?.handleAvancar) {
            ref.current?.handleAvancar();
          }
        }}
        fontSize="14px"
      >
        Avançar
      </Button>
      {isLargerThan700 && (
        <Icon
          cursor="pointer"
          boxSize="20px"
          color="gray.500"
          onClick={() => {
            ModalAtencaoLimiteProdutos();
          }}
          as={BiHelpCircle}
        />
      )}
    </>
  );
};
