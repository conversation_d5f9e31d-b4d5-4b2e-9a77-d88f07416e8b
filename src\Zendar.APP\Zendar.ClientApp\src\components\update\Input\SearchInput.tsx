import {
  Input,
  InputProps,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Icon,
} from '@chakra-ui/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { IconType } from 'icons/types';

import { LupaIcon } from 'icons';

interface SearchInputProps extends InputProps {
  name: string;
  onEnterKeyPress?: () => void;
  exibirTrailingIcon?: boolean;
  trailingIcon?: IconType;
  leadingIcon?: IconType;
}

export const SearchInput = ({
  size,
  name,
  onEnterKeyPress,
  onKeyPress,
  onBlur,
  exibirTrailingIcon = false,
  leadingIcon = LupaIcon,
  trailingIcon,
  ...rest
}: SearchInputProps) => {
  const { register } = useFormContext();

  const fieldProps = register(name);

  return (
    <InputGroup size={size}>
      <InputLeftElement pointerEvents="none" mx="2.5">
        <Icon as={leadingIcon} boxSize="5" color="#BBBBBB" />
      </InputLeftElement>

      <Input
        type="search"
        pl="12"
        {...rest}
        onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
          if (e.key === 'Enter' && onEnterKeyPress) {
            e.currentTarget.value = e.currentTarget.value.trim();

            onEnterKeyPress();
          }

          if (onKeyPress) onKeyPress(e);
        }}
        {...fieldProps}
        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
          e.currentTarget.value = e.currentTarget.value.trim();

          if (onBlur) onBlur(e);

          fieldProps.onBlur(e);
        }}
      />
      {exibirTrailingIcon && (
        <InputRightElement pointerEvents="none" mx="2.5">
          <Icon as={trailingIcon} boxSize="5" />
        </InputRightElement>
      )}
    </InputGroup>
  );
};
