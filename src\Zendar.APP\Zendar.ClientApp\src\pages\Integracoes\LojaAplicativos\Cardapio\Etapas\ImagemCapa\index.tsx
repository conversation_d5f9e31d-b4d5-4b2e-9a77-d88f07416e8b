import {
  Text,
  VStack,
  useMediaQuery,
  Flex,
  Button,
  Icon,
  Box,
} from '@chakra-ui/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  atualizarImagensCapaDelivery,
  obterCapaSalva,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import {
  IconeEtapaCapaMobile,
  IconeEtapaCapaWeb,
  IconeEtapaImagemCapa,
} from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import { ImagemAdicionarIcon, LixeiraIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';
import { processarImagemUpload } from '../utils/imagem';

export const ImagemCapa = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm();
  const { handleSubmit, watch, setValue } = formMethods;

  const capaWeb = watch('capaWeb');
  const capaMobile = watch('capaMobile');

  const inputFileImagemCapaWebRef = useRef<HTMLInputElement>(null);
  const inputFileImagemCapaMobileRef = useRef<HTMLInputElement>(null);

  const handleAvancar = handleSubmit(async (data) => {
    setIsLoading(true);
    const { capaWeb, capaMobile } = data;

    const capaWebAtualizada = capaWeb?.startsWith('data:image')
      ? capaWeb?.split(',')[1]
      : capaWeb;

    const capaMobileAtualizada = capaMobile?.startsWith('data:image')
      ? capaMobile?.split(',')[1]
      : capaMobile;

    const response = await atualizarImagensCapaDelivery(
      capaWebAtualizada || '',
      capaMobileAtualizada || ''
    );

    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_SELECAO_LOJA);
    }
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_LOGO);
  };

  const removerImagem = (nomeImagem: string) => {
    setValue(nomeImagem, '');
  };

  const salvarImagem = async (nomeImagem: string, imagem: string) => {
    setValue(nomeImagem, imagem);
  };

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    nomeImagem: string,
    resolucaoMaxima: {
      widthImage: number;
      heightImage: number;
    }
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await processarImagemUpload(
      file,
      nomeImagem,
      resolucaoMaxima,
      salvarImagem
    );
  };

  const buscarImagensCapaAtual = useCallback(async () => {
    setIsLoading(true);
    const response = await obterCapaSalva();
    const { mobile, web } = response || {};
    if (web) {
      setValue('capaWeb', web);
    }
    if (mobile) {
      setValue('capaMobile', mobile);
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    buscarImagensCapaAtual();
  }, [buscarImagensCapaAtual]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Imagem de capa"
          handleVoltar={handleVoltar}
          icon={IconeEtapaImagemCapa}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack
            mb={['0', '22px', '22px']}
            color="primary.50"
            fontSize="14px"
            justifyContent="center"
            spacing="24px"
          >
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
              whiteSpace="pre-line"
              w={isLargerThan700 ? '560px' : 'full'}
            >
              {`Personalize o site com um banner de destaque. A imagem será exibida no topo da página, próxima ao logo e ao nome. Siga as resoluções recomendadas.
              → Utilize imagens com extensão JPG, JPEG ou PNG
              → Tamanho máximo do arquivo de 500 kb`}
            </Text>
          </VStack>
          <Flex flexDir="column" w="full" align="center">
            <VStack
              color="black"
              fontSize="14px"
              justifyContent="center"
              spacing="24px"
              mt="56px"
              borderBottom="1px solid"
              borderColor="gray.200"
              maxW="480px"
              align="center"
              pb="32px"
              flexWrap="wrap"
            >
              <Flex
                gap="20px"
                align="center"
                flexWrap="wrap"
                w="full"
                flexDir={['column', 'row', 'row']}
              >
                <Box w="40px">
                  <IconeEtapaCapaWeb width="40px" height="40px" />
                </Box>
                <Flex flexDir="column" w="175px" letterSpacing="0px">
                  <Text>Imagem para versão web</Text>
                  <Text>
                    Resolução:{' '}
                    <Text as="span" fontWeight="bold">
                      1920 x 150px
                    </Text>
                  </Text>
                </Flex>
                <Flex align="center" gap="8px">
                  <Button
                    leftIcon={<ImagemAdicionarIcon strokeWidth="2px" />}
                    color="white"
                    colorScheme={capaWeb ? 'blue' : 'aquamarine'}
                    w="200px"
                    height="32px"
                    fontWeight="normal"
                    fontSize="14px"
                    onClick={() => {
                      if (inputFileImagemCapaWebRef.current) {
                        inputFileImagemCapaWebRef.current.click();
                      }
                    }}
                  >
                    {capaWeb ? 'Alterar imagem' : 'Adicionar imagem'}
                  </Button>
                  <input
                    tabIndex={-1}
                    onChange={(event) => {
                      handleFileChange(event, 'capaWeb', {
                        widthImage: 1920,
                        heightImage: 150,
                      });
                    }}
                    style={{
                      display: 'none',
                    }}
                    multiple={false}
                    ref={inputFileImagemCapaWebRef}
                    type="file"
                    accept="image/webp, image/avif, image/png, image/jpeg, image/jpg"
                  />
                  {capaWeb ? (
                    <Icon
                      as={LixeiraIcon}
                      boxSize="16px"
                      cursor="pointer"
                      onClick={() => {
                        removerImagem('capaWeb');
                      }}
                    />
                  ) : (
                    <Box w="16px" />
                  )}
                </Flex>
              </Flex>
            </VStack>
            <VStack
              mb={['0', '22px', '22px']}
              color="black"
              fontSize="14px"
              justifyContent="center"
              spacing="24px"
              mt="28px"
              borderBottom="1px solid"
              borderColor="gray.200"
              maxW="480px"
              pb="32px"
            >
              <Flex
                gap="20px"
                align="center"
                w="full"
                flexDir={['column', 'row', 'row']}
              >
                <Box w="40px">
                  <IconeEtapaCapaMobile width="40px" height="40px" />
                </Box>
                <Flex flexDir="column" w="175px" letterSpacing="0px">
                  <Text>Imagem para versão mobile</Text>
                  <Text>
                    Resolução:{' '}
                    <Text as="span" fontWeight="bold">
                      500 x 150px
                    </Text>
                  </Text>
                </Flex>
                <Flex align="center" gap="8px">
                  <Button
                    leftIcon={<ImagemAdicionarIcon strokeWidth="2px" />}
                    color="white"
                    colorScheme={capaMobile ? 'blue' : 'aquamarine'}
                    w="200px"
                    height="32px"
                    fontWeight="normal"
                    fontSize="14px"
                    onClick={() => {
                      if (inputFileImagemCapaMobileRef.current) {
                        inputFileImagemCapaMobileRef.current.click();
                      }
                    }}
                  >
                    {capaMobile ? 'Alterar imagem' : 'Adicionar imagem'}
                  </Button>
                  <input
                    tabIndex={-1}
                    onChange={(event) => {
                      handleFileChange(event, 'capaMobile', {
                        widthImage: 500,
                        heightImage: 150,
                      });
                    }}
                    style={{
                      display: 'none',
                    }}
                    multiple={false}
                    ref={inputFileImagemCapaMobileRef}
                    type="file"
                    accept="image/webp, image/avif, image/png, image/jpeg, image/jpg"
                  />
                  {capaMobile ? (
                    <Icon
                      as={LixeiraIcon}
                      boxSize="16px"
                      cursor="pointer"
                      onClick={() => {
                        removerImagem('capaMobile');
                      }}
                    />
                  ) : (
                    <Box w="16px" />
                  )}
                </Flex>
              </Flex>
            </VStack>
          </Flex>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
