import { Flex, Text, Icon, Box } from '@chakra-ui/react';
import { useImperativeHandle, useCallback } from 'react';
import { FiChevronLeft } from 'react-icons/fi';

import { useTrayEtapasContext } from 'store/Tray';

import { IconType } from 'icons/types';

type HeaderSmartPosProps = {
  icon: IconType;
  iconSize?: number;
  title: string;
  handleVoltar?: () => void;
  colorIcon?: string;
  width?: string | Array<string>;
};

export const Header = ({
  icon,
  title,
  iconSize = 40,
  handleVoltar,
  colorIcon = 'primary.50',
  width = ['full', '600px', '600px'],
}: HeaderSmartPosProps) => {
  const { ref } = useTrayEtapasContext();

  const podeVoltar = useCallback(() => {
    if (ref.current?.handleVoltar && handleVoltar) {
      return true;
    }
    return false;
  }, [ref, handleVoltar])();

  useImperativeHandle(ref, () => ({
    handleVoltar,
  }));

  return (
    <Box
      w={width}
      borderBottomStyle="solid"
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      mb="24px"
    >
      <Flex
        justifyContent="center"
        w="full"
        alignItems="center"
        cursor="pointer"
      >
        <Icon
          boxSize={`${iconSize}px`}
          size={iconSize}
          color={colorIcon}
          as={icon}
        />
      </Flex>

      <Flex
        mb="8px"
        alignItems="center"
        justifyContent={!podeVoltar ? 'space-between' : 'center'}
      >
        {podeVoltar && (
          <Flex
            align="center"
            onClick={handleVoltar}
            cursor="pointer"
            h="50px"
            pt="8px"
          >
            <Icon
              color="black"
              display={['none', 'none', 'flex']}
              boxSize="30px"
              onClick={handleVoltar}
              cursor="pointer"
              as={FiChevronLeft}
            />
          </Flex>
        )}

        <Box w="full">
          <Text
            textAlign="center"
            fontSize={['20px', '20px', '28px']}
            fontWeight="bold"
            marginTop="8px"
          >
            {title}
          </Text>
        </Box>

        {podeVoltar && <Box h="full" w="20px" cursor="pointer" />}
      </Flex>
    </Box>
  );
};
