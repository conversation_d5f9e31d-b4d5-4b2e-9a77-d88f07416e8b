import { TrayEtapasContext, TrayEtapasProvider } from 'store/Tray';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';

import { LayoutTray } from './Layout';
import { PassoAPasso } from './PassoAPasso';

export const TrayEtapas = () => {
  return (
    <>
      <TrayEtapasProvider>
        <TrayEtapasContext.Consumer>
          {({ activeStep, isLoading }) => (
            <LayoutTray>
              {isLoading && <LoadingPadrao />}
              <PassoAPasso passoAtual={activeStep} />
            </LayoutTray>
          )}
        </TrayEtapasContext.Consumer>
      </TrayEtapasProvider>
    </>
  );
};
