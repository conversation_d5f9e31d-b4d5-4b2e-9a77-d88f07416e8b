import { VStack, Text } from '@chakra-ui/react';
import { useCallback, useImperativeHandle, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { getMetaComissao } from 'services/tray';

import { useTrayEtapasContext } from 'store/Tray';

import SelectPadrao from 'components/PDV/Select/SelectPadrao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { MetasIcon } from 'icons';

import { Header } from '../Layout/Header';

import { yupResolver, FormData, dataComissaoVendas } from './validationForms';

type OptionsVendedores = {
  label: string;
  value: string;
};

export type OptionResponseVendedores = {
  id: string;
  nome: string;
};

export const ComissaoVendas = () => {
  const { ref, setActiveStep, setIsLoading } = useTrayEtapasContext();

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
  });

  const { handleSubmit: onSubmit, reset } = formMethods;

  const handleAvancar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.LOCAL_ESTOQUE);
  }, [setActiveStep]);

  const handleVoltar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.VENDEDOR);
  }, [setActiveStep]);

  const getComissaoVendas = useCallback(async () => {
    setIsLoading(true);

    const response = await getMetaComissao();

    if (response !== null) {
      reset({
        considerarFaturamento: response.considerarFaturamento,
        pagarComissao: response.pagarComissao,
      });
    }

    setIsLoading(false);
  }, [setIsLoading, reset]);

  const handleSubmit = onSubmit(async (data) => {
    setIsLoading(true);

    const response = await api.put<void, ResponseApi<boolean>>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_COMISSAO_VENDA_ALTERAR,
      { ...data }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
        setIsLoading(false);
      }

      if (response.sucesso) {
        handleAvancar();
      }
    }

    setIsLoading(false);
  });

  useImperativeHandle(ref, () => ({
    handleAvancar: handleSubmit,
    handleVoltar,
  }));

  useEffect(() => {
    getComissaoVendas();
  }, [getComissaoVendas]);

  return (
    <>
      <Header
        title="Comissão de vendas"
        handleVoltar={handleVoltar}
        icon={MetasIcon}
      />
      <FormProvider {...formMethods}>
        <VStack
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          fontSize="14px"
          w="full"
          alignItems="left"
          spacing="24px"
        >
          <Text
            mb="4px"
            color="primary.50"
            textAlign="left"
            letterSpacing="0px"
          >
            Informe se o faturamento da integração será incluído no programa de
            metas e comissões. (Válido apenas caso tenha um programa de metas já
            configurado para a empresa).
          </Text>

          <SelectPadrao
            id="pagarComissao"
            name="pagarComissao"
            label="Pagar comissão sobre as vendas da integração?"
            asControlledByObject={false}
            colSpan={12}
            helperTextLabel="Ao selecionar “SIM” todas as vendas da integração entrarão no cálculo de comissão para o vendedor informado no passo anterior. (Se a empresa tiver um plano de metas configurado)"
            options={dataComissaoVendas}
            valueDefault={false}
          />

          <SelectPadrao
            id="considerarFaturamento"
            name="considerarFaturamento"
            helperTextLabel="Ao selecionar “SIM” todas as vendas da integração serão consideradas no cálculo de meta geral de vendas da empresa. (Se tiver um plano de metas configurado)"
            label="Considerar o faturamento da integração no cálculo de metas gerais da empresa?"
            asControlledByObject={false}
            colSpan={12}
            options={dataComissaoVendas}
            valueDefault={false}
          />
        </VStack>
      </FormProvider>
    </>
  );
};
