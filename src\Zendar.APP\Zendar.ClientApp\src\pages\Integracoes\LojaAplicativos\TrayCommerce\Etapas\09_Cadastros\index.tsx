import { VStack, Text, Flex, Box, Icon, Tooltip } from '@chakra-ui/react';
import { useCallback, useImperativeHandle, useState, useEffect } from 'react';
import { BsArrowRight } from 'react-icons/bs';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { getTipoProdutoTray } from 'services/tray';

import { ContainerIntegracaoFixo } from 'store/Cardapio/Etapas/EtapasContext';
import { useTrayEtapasContext } from 'store/Tray';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { CadastrosIcon } from 'icons';

import { Header } from '../Layout/Header';

import { dataDefaultCadastro, TipoUtilizacaoCadastro } from './validationForms';

export const TipoCadastro = () => {
  const [listcadastro, setListcadastro] = useState(dataDefaultCadastro);

  const { ref, setActiveStep, setIsLoading, setIsProdutoSistema } =
    useTrayEtapasContext();

  const handleAvancar = useCallback(
    (route: number) => {
      setActiveStep(route);
    },
    [setActiveStep]
  );

  const handleVoltar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.TABELA_PRECO);
  }, [setActiveStep]);

  const handleSelecionarCadastro = useCallback((index: number) => {
    setListcadastro((prev) =>
      prev.map((itemCadastro, indexCadastro) => ({
        ...itemCadastro,
        isChecked: index === indexCadastro,
      }))
    );
  }, []);

  const getObterTipoCadastro = useCallback(async () => {
    setIsLoading(true);

    const response = await getTipoProdutoTray();

    if (response !== null) {
      const data = dataDefaultCadastro.map((cadastroItem) => {
        return {
          ...cadastroItem,
          isChecked: cadastroItem.value === response.tipoCadastro,
        };
      });
      setListcadastro(data);
    }

    setIsLoading(false);
  }, [setIsLoading]);

  const handleSubmit = async () => {
    setIsLoading(true);

    const tipoCadastro = listcadastro.find(
      (itemCadastro) => itemCadastro.isChecked
    )?.value;

    const response = await api.put<void, ResponseApi<boolean>>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_TIPO_CADASTRO_ALTERAR,
      {
        tipoCadastro,
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
        setIsLoading(false);
      }

      if (response.sucesso) {
        let route;
        if (TipoUtilizacaoCadastro.PRODUTO_ZENDAR === tipoCadastro) {
          route = ETAPA_GUIA_INTEGRACAO_TRAY.LISTA_PRODUTO_SISTEMA;
        } else {
          route = ETAPA_GUIA_INTEGRACAO_TRAY.BUSCAR_PRODUTO;
        }

        handleAvancar(route);
      }
    }

    setIsLoading(false);
  };

  useImperativeHandle(ref, () => ({
    handleAvancar: handleSubmit,
    handleVoltar,
  }));

  useEffect(() => {
    setIsProdutoSistema(false);
    getObterTipoCadastro();
  }, [getObterTipoCadastro, setIsProdutoSistema]);

  return (
    <ContainerIntegracaoFixo>
      <Header
        title="Cadastros"
        handleVoltar={handleVoltar}
        icon={CadastrosIcon}
      />

      <VStack
        px="5px"
        fontSize="14px"
        fontWeight="normal"
        spacing="24px"
        maxW="600px"
      >
        {listcadastro.map((cadastro, index) => (
          <Flex
            borderWidth="1px"
            borderStyle="solid"
            opacity={cadastro.isDisable ? '0.6' : '1'}
            borderColor={
              cadastro.isChecked ? 'rgba(85, 2, 178, 0.5)' : 'gray.50'
            }
            _hover={{
              borderColor: cadastro.isDisable
                ? 'none'
                : 'rgba(85, 2, 178, 0.5)',
              transition: 'all 0.2s',
            }}
            transition="all 0.2s"
            bg={cadastro.isChecked ? 'blue.50' : ''}
            cursor={cadastro.isDisable ? 'not-allowed' : 'pointer'}
            pt="16px"
            pb="16px"
            px="10px"
            w="full"
            onClick={() => {
              if (!cadastro.isDisable) {
                handleSelecionarCadastro(index);
              }
            }}
            justifyContent="flex-start"
          >
            <Box mr="9.5px">
              <Icon boxSize="20px" color="primary.50" as={BsArrowRight} />
            </Box>
            <Box letterSpacing="0px">
              <Flex alignItems="center">
                <Box mr="10px">
                  <Text
                    color="primary.50"
                    fontSize="18px"
                    fontWeight="semibold"
                  >
                    {cadastro.title}
                  </Text>
                </Box>
                {cadastro.isRecomendado && (
                  <Tooltip
                    hasArrow
                    placement="right-start"
                    borderRadius="3px"
                    fontSize="12px"
                    padding="18px 18px 22px 24px"
                    bgColor="#0F0F0F"
                    label="Os produtos cadastrados no sistema ficam organizados dentro da estrutura ideal para vendas e emissão fiscal. Cadastrar primeiro no sistema e depois enviar para a loja online é a forma mais indicada para manter os ítens padronizados e garantir que todos os requisitos obrigatórios sejam preenchidos corretamente."
                  >
                    <Flex
                      justifyContent="center"
                      alignItems="center"
                      h="16px"
                      p="8px"
                      bg="primary.50"
                      color="white"
                      borderRadius="10px"
                    >
                      <Text fontSize="12px">Recomendado</Text>
                    </Flex>
                  </Tooltip>
                )}
              </Flex>
              <Text
                letterSpacing="0px"
                color="gray.700"
                fontSize="14px"
                mt="4px"
              >
                {cadastro.description}
              </Text>
            </Box>
          </Flex>
        ))}
      </VStack>
    </ContainerIntegracaoFixo>
  );
};
