import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { CadastrarTabelaPreco } from '../CadastrarTabelaPreco';
import { CorPersonalizada } from '../CorPersonalizada';
import { DescricaoLoja } from '../DescricaoLoja';
import { DominioPersonalizado } from '../DominioPersonalizado';
import { GuiaIntegracao } from '../GuiaIntegracao';
import { HorarioAtendimentoEtapa } from '../HorarioAtendimento';
import { ImagemCapa } from '../ImagemCapa';
import { ImagemParaLogo } from '../ImagemLogo';
import { ImagemLojas } from '../ImagemLojas';
import { MidiasSociaisEtapa } from '../MidiasSociais';
import { PerfilLoja } from '../PerfilLoja';
import { Produtos } from '../Produtos';
import { SelecionarProdutos } from '../SelecionarProduto';
import { TabelaPreco } from '../TabelaPrecos';
import { TempoPreparo } from '../TempoPreparo';

type PassosIntegracaoProps = {
  passoAtual: number;
};

export const PassoAPasso = ({ passoAtual }: PassosIntegracaoProps) => {
  switch (passoAtual) {
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.INICIO:
      return <GuiaIntegracao />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.IDENTIFICADOR_URL:
      return <DominioPersonalizado />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.PERFIL_LOJA:
      return <PerfilLoja />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.DESCRICAO_LOJA:
      return <DescricaoLoja />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_LOGO:
      return <ImagemParaLogo />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_CAPA:
      return <ImagemCapa />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_SELECAO_LOJA:
      return <ImagemLojas />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.COR_PERSONALIZADA:
      return <CorPersonalizada />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.TEMPO_PREPARO:
      return <TempoPreparo />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.HORARIO_FUNCIONAMENTO:
      return <HorarioAtendimentoEtapa />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.TABELA_PRECO:
      return <TabelaPreco />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.CADASTRAR_TABELA_PRECO:
      return <CadastrarTabelaPreco />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.FORMAS_RECEBIMENTO:
      return null;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.MIDIAS_SOCIAIS:
      return <MidiasSociaisEtapa />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.PRODUTOS:
      return <Produtos />;
    case ETAPA_GUIA_INTEGRACAO_CARDAPIO.SELECIONAR_PRODUTOS:
      return <SelecionarProdutos />;

    default:
      return null;
  }
};
