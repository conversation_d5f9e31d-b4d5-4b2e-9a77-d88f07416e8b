import { VStack, Text, Box, Link, useMediaQuery } from '@chakra-ui/react';
import { useImperativeHandle, useCallback } from 'react';

import { ContainerIntegracaoFixo } from 'store/Cardapio/Etapas/EtapasContext';
import { useTrayEtapasContext } from 'store/Tray';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { MenuIntegracoes } from 'icons';

import { Header } from '../Layout/Header';

export const GuiaIntegracao = () => {
  const { ref, setActiveStep } = useTrayEtapasContext();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const handleAvancar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.PORTAL_CLIENTE);
  }, [setActiveStep]);

  useImperativeHandle(ref, () => ({
    handleAvancar,
  }));

  return (
    <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
      <Header title="Guia de integração" icon={MenuIntegracoes} />
      <VStack
        pl={['10px', '10px', '20px']}
        pr={['10px', '10px', '20px']}
        color="purple.500"
        fontSize="14px"
        fontWeight="normal"
        spacing="24px"
        maxW="600px"
      >
        <Text textAlign="left" letterSpacing="0px">
          Siga as instruções do nosso guia de integração para configurar passo a
          passo a sincronização entre sua loja virtual e o sistema.
        </Text>
        <Text textAlign="left" letterSpacing="0px">
          O primeiro passo é ter um plano contratado na plataforma. Se você já
          tem uma conta na Tray, clique em “Iniciar Guia de Integração”.
        </Text>

        <Box>
          <Text textAlign="left" fontWeight="bold">
            Se você ainda não tem um plano contratado na TRAY
          </Text>
          <Text textAlign="left" letterSpacing="0px">
            É obrigatório contratar um plano na plataforma antes de iniciar o
            guia. A integração com o sistema depende de dados personalizados que
            serão encontrados assim que você receber o acesso ao portal do
            cliente Tray. Faça a contratação antes de iniciar o processo.
          </Text>
        </Box>

        <Text w="full" textAlign="left" letterSpacing="0px">
          Acesse o{' '}
          <Link
            href="https://www.tray.com.br/"
            target="_blank"
            style={{
              textDecoration: 'underline',
              fontStyle: 'italic',
              cursor: 'pointer',
            }}
            _hover={{ color: 'purple.400' }}
          >
            site da Tray
          </Link>{' '}
          para conhecer preços e contratar um plano. Depois de contratar,
          retorne até aqui para iniciar a configuração.
        </Text>
      </VStack>
    </ContainerIntegracaoFixo>
  );
};
