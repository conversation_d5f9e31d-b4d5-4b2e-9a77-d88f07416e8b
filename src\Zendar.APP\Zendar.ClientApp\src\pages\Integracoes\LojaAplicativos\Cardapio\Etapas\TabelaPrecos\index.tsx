import { VStack, Text, Flex, Box, Icon, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useImperativeHandle, useState, useEffect } from 'react';
import { BsArrowRight } from 'react-icons/bs';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { atualizarTabelaPreco, obterDadosIntegracao } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import PlanoContratacaoEnum from 'constants/enum/planoContratacao';
import { TabelaPrecosIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { ModalTabelaCriada } from './ModalTabelaCriada';
import { dataDefaultTabelaPreco, TabelaPrecoValue } from './validationForms';

export type OptionResponseProps = {
  id: string;
  nome: string;
};

export const TabelaPreco = () => {
  const [optionsTabelaPreco, setOptionsTabelaPreco] = useState(
    dataDefaultTabelaPreco
  );

  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const opcaoEscolhida = optionsTabelaPreco.find(
    (itemTabelaPreco) => itemTabelaPreco.isChecked
  );

  const handleAvancar = useCallback(
    (identificacao: number) => {
      setPassoAtual(identificacao);
    },
    [setPassoAtual]
  );

  const planoIsAtual = auth.getPlano();

  const isPlanoStart = planoIsAtual === PlanoContratacaoEnum.START;

  const atualizarTabelaCriada = useCallback(async () => {
    setIsLoading(true);
    const { dados } = await obterDadosIntegracao();

    let isPossuiTabelaPreco = false;

    try {
      const valueTabelaPreco = JSON.parse(dados.configuracoes) as {
        PossuiTabelaPrecoCriada: boolean;
      };
      isPossuiTabelaPreco = valueTabelaPreco.PossuiTabelaPrecoCriada;
    } catch (e) {
      isPossuiTabelaPreco = false;
    }

    setOptionsTabelaPreco((prev) =>
      prev.map((item) => ({
        ...item,
        isDisable:
          item.value === TabelaPrecoValue.CRIAR_TABELA_PRECO
            ? isPossuiTabelaPreco
            : item.isDisable,
      }))
    );
    setIsLoading(false);
  }, [obterDadosIntegracao, setIsLoading]);

  const handleVoltar = useCallback(() => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.HORARIO_FUNCIONAMENTO);
  }, [setPassoAtual]);

  const handleSelecionarCadastro = useCallback((index: number) => {
    setOptionsTabelaPreco((prev) =>
      prev.map((itemCadastro, indexCadastro) => ({
        ...itemCadastro,
        isChecked: index === indexCadastro,
      }))
    );
  }, []);

  const handleSubmit = useCallback(async () => {
    setIsLoading(true);

    if (opcaoEscolhida?.value === undefined) {
      toast.warning('Escolha uma opção para avançar a etapa');
      setIsLoading(false);
      return;
    }

    if (opcaoEscolhida.value === TabelaPrecoValue.SELECIONAR_TABELA_EXISTENTE) {
      setIsLoading(false);
      const dados = await ModalTabelaCriada();

      if (dados.sucesso && dados.tabelaPreco) {
        const sucesso = await atualizarTabelaPreco(dados.tabelaPreco);
        if (sucesso) {
          setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.MIDIAS_SOCIAIS);
        }
      }
      setIsLoading(false);
    } else {
      handleAvancar(ETAPA_GUIA_INTEGRACAO_CARDAPIO.CADASTRAR_TABELA_PRECO);
    }

    setIsLoading(false);
  }, [handleAvancar, opcaoEscolhida, setIsLoading]);

  useEffect(() => {
    if (isPlanoStart) {
      handleAvancar(ETAPA_GUIA_INTEGRACAO_CARDAPIO.MIDIAS_SOCIAIS);
    }
  }, [handleAvancar, isPlanoStart, setPassoAtual]);

  useEffect(() => {
    atualizarTabelaCriada();
  }, [atualizarTabelaCriada]);

  return (
    <>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Tabela de preços"
          handleVoltar={handleVoltar}
          icon={TabelaPrecosIcon}
        />

        <VStack fontSize="14px" maxW="600px" alignItems="left" spacing="24px">
          {optionsTabelaPreco.map((cadastro, index) => (
            <Flex
              borderWidth="1px"
              borderStyle="solid"
              opacity={cadastro.isDisable ? '0.6' : '1'}
              borderColor={
                cadastro.isChecked ? 'rgba(85, 2, 178, 0.5)' : 'gray.50'
              }
              _hover={{
                borderColor: cadastro.isDisable
                  ? 'none'
                  : 'rgba(85, 2, 178, 0.5)',
                transition: 'all 0.2s',
              }}
              transition="all 0.2s"
              bg={cadastro.isChecked ? 'blue.50' : ''}
              cursor={cadastro.isDisable ? 'not-allowed' : 'pointer'}
              pt="16px"
              pl={['10px', '10px', '16px']}
              pr={['10px', '10px', '16px']}
              pb="16px"
              w="full"
              onClick={() => {
                if (!cadastro.isDisable) {
                  handleSelecionarCadastro(index);
                }
              }}
              justifyContent="flex-start"
            >
              <Box mr="9.5px">
                <Icon boxSize="20px" color="primary.50" as={BsArrowRight} />
              </Box>
              <Box>
                <Flex alignItems="baseline">
                  <Box mr="10px">
                    <Text
                      color="primary.50"
                      fontSize="18px"
                      fontWeight="semibold"
                    >
                      {cadastro.title}
                    </Text>
                  </Box>
                </Flex>
                <Text letterSpacing="0px" color="gray.700" fontSize="14px">
                  {cadastro.descricao}
                </Text>
                <Text as="span" fontWeight="semibold" letterSpacing="0px">
                  {cadastro.subdescricao}
                </Text>
              </Box>
            </Flex>
          ))}
        </VStack>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleSubmit}
      />
    </>
  );
};
