import { useCallback, useRef } from 'react';

interface BarcodeDetectionOptions {
  minLength?: number;
  maxTimeBetweenChars?: number;
  minSpeed?: number;
  instantInputThreshold?: number;
}

interface BarcodeDetectionResult {
  isFromScanner: boolean;
  inputValue: string;
}

export const useBarcodeDetection = (options: BarcodeDetectionOptions = {}) => {
  const {
    minLength = 8,
    maxTimeBetweenChars = 50,
    minSpeed = 10,
    instantInputThreshold = 100, // tempo em ms para considerar entrada instantânea
  } = options;

  const inputStartTime = useRef<number>(0);
  const lastCharTime = useRef<number>(0);
  const charTimes = useRef<number[]>([]);
  const inputBuffer = useRef<string>('');
  const isTypingFast = useRef<boolean>(false);
  const lastInputTime = useRef<number>(0);

  const detectBarcodeInput = useCallback(
    (inputValue: string): BarcodeDetectionResult => {
      const now = Date.now();
      const currentLength = inputValue.length;
      const previousLength = inputBuffer.current.length;

      if (currentLength === 0 || currentLength < previousLength) {
        inputStartTime.current = now;
        lastCharTime.current = now;
        charTimes.current = [];
        inputBuffer.current = inputValue;
        isTypingFast.current = false;
        lastInputTime.current = now;
        return { isFromScanner: false, inputValue };
      }

      if (currentLength === 1 || currentLength - previousLength > 1) {
        inputStartTime.current = now;
        lastCharTime.current = now;
        charTimes.current = [now];
        inputBuffer.current = inputValue;
        lastInputTime.current = now;

        if (currentLength - previousLength > 3 && currentLength >= minLength) {
          isTypingFast.current = true;
          return { isFromScanner: true, inputValue };
        }

        if (currentLength >= minLength && previousLength === 0) {
          const timeSinceLastInput = now - lastInputTime.current;
          if (timeSinceLastInput < instantInputThreshold) {
            isTypingFast.current = true;
            return { isFromScanner: true, inputValue };
          }
        }

        isTypingFast.current = false;
        return { isFromScanner: false, inputValue };
      }

      if (currentLength > previousLength) {
        const timeSinceLastChar = now - lastCharTime.current;
        charTimes.current.push(now);
        lastCharTime.current = now;
        inputBuffer.current = inputValue;

        if (timeSinceLastChar > 0 && timeSinceLastChar <= maxTimeBetweenChars) {
          isTypingFast.current = true;
        } else if (timeSinceLastChar > maxTimeBetweenChars * 3) {
          isTypingFast.current = false;
        }
      }

      if (currentLength < minLength) {
        return { isFromScanner: false, inputValue };
      }

      const totalTime = now - inputStartTime.current;
      const avgSpeed = totalTime > 0 ? (currentLength / totalTime) * 1000 : 0;

      const isFromScanner =
        (isTypingFast.current && avgSpeed >= minSpeed) ||
        avgSpeed >= minSpeed * 2 ||
        (totalTime <= instantInputThreshold && currentLength >= minLength);

      return {
        isFromScanner,
        inputValue,
      };
    },
    [maxTimeBetweenChars, minLength, minSpeed, instantInputThreshold]
  );

  const resetDetection = useCallback(() => {
    inputStartTime.current = 0;
    lastCharTime.current = 0;
    charTimes.current = [];
    inputBuffer.current = '';
    isTypingFast.current = false;
    lastInputTime.current = 0;
  }, []);

  return {
    detectBarcodeInput,
    resetDetection,
  };
};
