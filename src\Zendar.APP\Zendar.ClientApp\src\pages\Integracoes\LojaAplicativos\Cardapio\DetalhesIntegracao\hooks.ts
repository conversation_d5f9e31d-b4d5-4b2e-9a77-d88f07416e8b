import { useCallback, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';
import { obterEtapaAtual } from 'services/cardapio';

import { ModalConfirmarAcao } from 'components/update/Modal/ModalConfirmarAcao';
import { ModalRemoverDadosIntegracao } from 'components/update/Modal/ModalRemoverDadosIntegracao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteFuncionalidades from 'constants/permissoes';
import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';

export type DadosIntegracao = {
  id: string;
  dataAtivacao: Date;
  ativo: boolean;
  sincronizacaoHabilitada: boolean;
};

export const useCardapioDadosAtivacao = () => {
  const [dataAtivacao, setDataAtivacao] = useState<Date>(new Date());
  const [estaAtivada, setEstaAtivada] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const history = useHistory();

  const possuiPermissaoExcluirIntegracao = auth.possuiPermissao(
    ConstanteFuncionalidades.CARDAPIO_EXCLUIR
  ).permitido;

  const listaItensSeIntegracaoAtiva = [
    'Para acessar o painel de configurações da integração, clique em: Vendas > Cardápio',
    'Para reset da configuração ou para excluir esta integração, clique em “Cancelar integração” no canto superior direito da página.',
    'Para outras dúvidas ou problemas, acesse nossa central de ajuda ou entre em contato com nossa equipe técnica.',
  ];

  const listaItensSeIntegracaoNaoAtivada = [
    'Para ativar a integração, clique em “Ativar integração” no canto superior direito da página.',
    'Para outras dúvidas ou problemas, acesse nossa central de ajuda ou entre em contato com nossa equipe técnica.',
  ];

  const cancelarIntegracao = async () => {
    ModalRemoverDadosIntegracao({
      callback: async () => {
        setIsLoading(true);
        const response = await api.delete<void, ResponseApi>(
          ConstanteEnderecoWebservice.INTEGRACAO_DELIVERY_DELETAR_INTEGRACAO
        );
        if (response) {
          if (response.avisos) {
            response.avisos.forEach((aviso: string) => toast.warning(aviso));
          }
          if (response.sucesso) {
            history.push(ConstanteRotas.LOJA_APLICATIVOS);
          }
          setIsLoading(false);
        }
        setIsLoading(false);
      },
    });
  };

  const handleConfirmarCancelamento = () => {
    if (!possuiPermissaoExcluirIntegracao) {
      toast.warn(
        'Você não tem permissão para realizar essa ação. Consulte o administrador da conta.'
      );
      return;
    }

    ModalConfirmarAcao({
      mainText: 'Excluir ou Reiniciar',
      submitButtonText: 'Continuar',
      secondaryText:
        'Todos os dados armazenados e todo o processo de configuração serão excluídos. Essa ação não poderá ser desfeita. Deseja continuar?',
      variant: 'danger',
      callbackSubmit: cancelarIntegracao,
    });
  };

  const obterDadosIntegracao = useCallback(async () => {
    const response = await api.get<void, ResponseApi<DadosIntegracao>>(
      ConstanteEnderecoWebservice.INTEGRACAO_DELIVERY_OBTER_INTEGRACAO
    );

    if (response) {
      if (response.sucesso && response.dados) {
        return response.dados;
      }
    }
    return null;
  }, []);

  const obterStatusIntegracao = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<DadosIntegracao>>(
      ConstanteEnderecoWebservice.INTEGRACAO_DELIVERY_OBTER_INTEGRACAO
    );

    if (response?.sucesso && response?.dados) {
      setEstaAtivada(response.dados.ativo);
      if (response.dados.dataAtivacao && response.dados.ativo) {
        setDataAtivacao(response.dados.dataAtivacao);
      }
      const configEtapas = await obterEtapaAtual();

      if (configEtapas !== ETAPA_GUIA_INTEGRACAO_CARDAPIO.FINALIZADO) {
        setIsLoading(false);
        history.push(ConstanteRotasAlternativas.CARDAPIO_ETAPA);
      }

      setIsLoading(false);
      return;
    }

    if (!response.sucesso) {
      history.push(ConstanteRotasAlternativas.CARDAPIO_ETAPA);
      setIsLoading(false);
      return;
    }
  }, [history]);

  const handleAtivarIntegracao = useCallback(async () => {
    setIsLoading(true);
    const dadosIntegracao = await obterDadosIntegracao();

    if (dadosIntegracao) {
      const parametros = new URLSearchParams({
        integracaoId: dadosIntegracao.id ?? '',
      });

      const response = await api.post<void, ResponseApi>(
        `${
          ConstanteEnderecoWebservice.INTEGRACAO_ATIVAR
        }?${parametros.toString()}`
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso: string) => toast.warning(aviso));
        }
        if (response.sucesso) {
          setEstaAtivada(true);
          setDataAtivacao(new Date());
        }
      }
      setIsLoading(false);
      return;
    }
    toast.error('Não foi possível ativar a integração.');

    setIsLoading(false);
  }, [obterDadosIntegracao]);

  useEffect(() => {
    obterStatusIntegracao();
  }, [obterStatusIntegracao]);

  return {
    dataAtivacao,
    listaItensSeIntegracaoAtiva,
    listaItensSeIntegracaoNaoAtivada,
    handleConfirmarCancelamento,
    handleAtivarIntegracao,
    estaAtivada,
    isLoading,
  };
};
