# Detecção de Código de Barras - Melhorias

## Problemas Identificados no Hook Original

### 1. **Lógica de Reset Inadequada**
- O hook resetava para `isFromScanner: false` quando `currentLength === 1`, mesmo que fosse o início de uma leitura por scanner
- Isso causava falsos negativos para leitores que enviam dados rapidamente

### 2. **Não Detectava Entrada Instantânea**
- Muitos leitores modernos enviam o texto completo instantaneamente
- O hook original só detectava entrada caractere por caractere
- Não havia verificação para entrada de texto longo em tempo muito curto

### 3. **Critérios de Detecção Muito Restritivos**
- Exigia que TODAS as condições fossem atendidas simultaneamente
- Velocidade mínima muito baixa (10 chars/s)
- Não considerava diferentes padrões de leitores

### 4. **Falta de Detecção por Eventos de Teclado**
- Alguns leitores simulam digitação via eventos de teclado
- <PERSON> hook original só analisava mudanças no valor do input

## Melhorias Implementadas

### 1. **Hook `useBarcodeDetection` Melhorado**

#### Novas Funcionalidades:
- **Detecção de entrada instantânea**: Identifica quando texto longo é inserido rapidamente
- **Múltiplos critérios de detecção**: OR em vez de AND para os critérios
- **Melhor tratamento de reset**: Não reseta prematuramente
- **Novo parâmetro `instantInputThreshold`**: Configura tempo para entrada instantânea

#### Critérios de Detecção:
1. **Entrada de múltiplos caracteres**: Se recebe >3 caracteres de uma vez
2. **Entrada instantânea**: Texto longo inserido em <100ms
3. **Velocidade alta**: Velocidade ≥ 2x o mínimo configurado
4. **Digitação rápida consistente**: Tempo entre caracteres ≤ 50ms

### 2. **Novo Hook `useKeyboardBarcodeDetection`**

#### Funcionalidades:
- **Detecção por eventos de teclado**: Captura teclas pressionadas globalmente
- **Buffer de teclas**: Acumula teclas até encontrar tecla de finalização
- **Análise de velocidade**: Calcula tempo médio entre teclas
- **Auto-reset**: Limpa buffer automaticamente após timeout

#### Configurações:
- `minLength`: Tamanho mínimo do código (padrão: 8)
- `maxTimeBetweenKeys`: Tempo máximo entre teclas (padrão: 100ms)
- `endKeys`: Teclas que finalizam a leitura (padrão: Enter, Tab)
- `enabled`: Habilita/desabilita a detecção

### 3. **Componente de Exemplo**

O componente `BarcodeInputExample` demonstra:
- Uso combinado dos dois hooks
- Feedback visual da detecção
- Diferentes métodos de detecção
- Configuração flexível

## Como Usar

### Hook Básico (Melhorado)
```typescript
const { detectBarcodeInput, resetDetection } = useBarcodeDetection({
  minLength: 8,
  maxTimeBetweenChars: 50,
  minSpeed: 10,
  instantInputThreshold: 100, // NOVO!
});

const handleInputChange = (event) => {
  const result = detectBarcodeInput(event.target.value);
  if (result.isFromScanner) {
    // Código veio de leitor!
    handleBarcodeScanned(result.inputValue);
  }
};
```

### Hook de Teclado (Novo)
```typescript
const handleBarcodeScanned = useCallback((result) => {
  if (result.isFromScanner) {
    console.log('Código escaneado:', result.scannedValue);
  }
}, []);

useKeyboardBarcodeDetection(handleBarcodeScanned, {
  minLength: 8,
  maxTimeBetweenKeys: 100,
  endKeys: ['Enter', 'Tab'],
  enabled: true,
});
```

### Uso Combinado
```typescript
// Para máxima compatibilidade, use ambos os hooks
const inputDetection = useBarcodeDetection();
const keyboardDetection = useKeyboardBarcodeDetection(handleKeyboardBarcode);
```

## Configurações Recomendadas

### Para Códigos de Barras Padrão:
```typescript
{
  minLength: 8,
  maxTimeBetweenChars: 50,
  minSpeed: 10,
  instantInputThreshold: 100
}
```

### Para Códigos Mais Longos (EAN-13, etc.):
```typescript
{
  minLength: 12,
  maxTimeBetweenChars: 30,
  minSpeed: 15,
  instantInputThreshold: 80
}
```

### Para Leitores Mais Lentos:
```typescript
{
  minLength: 8,
  maxTimeBetweenChars: 100,
  minSpeed: 5,
  instantInputThreshold: 200
}
```

## Compatibilidade

- ✅ Leitores USB que simulam teclado
- ✅ Leitores que enviam dados instantaneamente
- ✅ Leitores que enviam caractere por caractere
- ✅ Leitores Bluetooth
- ✅ Aplicativos de câmera que inserem texto
- ✅ Entrada manual (detectada corretamente como não-scanner)

## Migração

Para migrar do hook antigo:
1. Substitua `useBarcodeDetection()` pela versão melhorada
2. Considere adicionar `useKeyboardBarcodeDetection` para maior compatibilidade
3. Ajuste os parâmetros conforme necessário
4. Teste com seus leitores específicos
