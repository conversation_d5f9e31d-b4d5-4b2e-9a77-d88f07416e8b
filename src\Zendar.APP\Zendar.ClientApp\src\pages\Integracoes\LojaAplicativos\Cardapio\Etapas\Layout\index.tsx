import { Flex, Text, Icon, Box, Button, useMediaQuery } from '@chakra-ui/react';
import { FiX, FiChevronLeft } from 'react-icons/fi';
import { useHistory } from 'react-router-dom';

import { useEtapasContext } from 'store/Cardapio/Etapas/EtapasContext';

import { IconeEtapaSelecaoProdutos } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas from 'constants/rotas';

import { useTotalProdutosSelecionados } from '../SelecionarProduto/components/CategoriaProduto/hooks/useTotalProdutosSelecionados';

import { Header } from './Header';

type LayoutEtapasProps = {
  children: React.ReactNode;
};

export const LayoutEtapas = ({ children }: LayoutEtapasProps) => {
  const history = useHistory();

  const { refHandleVoltar, passoAtual, setPassoAtual } = useEtapasContext();
  const { totalProdutosSelecionados } = useTotalProdutosSelecionados();

  const [isSmallerThan1000] = useMediaQuery('(max-width: 1000px)');
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const passoSelecionarProdutoQuandoTelaEstaMenor700 =
    passoAtual === ETAPA_GUIA_INTEGRACAO_CARDAPIO.SELECIONAR_PRODUTOS &&
    isLargerThan700;

  const handleSair = () => {
    history.push(ConstanteRotas.LOJA_APLICATIVOS);
  };

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.PRODUTOS);
  };

  return (
    <Flex
      pl={['10px', '10px', '40px']}
      bg="gray.50"
      pr={['10px', '10px', '40px']}
      w="full"
      flexDir="column"
      pt="24px"
      minH="100vh"
      h="full"
      alignItems="center"
      position="relative"
    >
      <Flex
        pb={passoSelecionarProdutoQuandoTelaEstaMenor700 ? '0px' : '24px'}
        alignItems="center"
        w="full"
        justifyContent="space-between"
        borderBottomStyle="solid"
        borderBottomWidth={
          passoSelecionarProdutoQuandoTelaEstaMenor700 ? '1px' : '0px'
        }
        borderBottomColor="gray.200"
        mb={passoSelecionarProdutoQuandoTelaEstaMenor700 ? '24px' : '0px'}
      >
        <Flex alignItems="center">
          <Button
            onClick={() => {
              if (refHandleVoltar.current?.handle) {
                refHandleVoltar.current?.handle();
              }
            }}
            variant="ghost"
            colorScheme="primary"
            _focus={{
              boxShadow: 'none',
            }}
          >
            <Icon boxSize="20px" as={FiChevronLeft} mr="6px" />
            <Text color="primary.50" fontSize="18px">
              Voltar
            </Text>
          </Button>
        </Flex>
        {passoSelecionarProdutoQuandoTelaEstaMenor700 && (
          <Flex
            align="center"
            justify="center"
            ml={[isSmallerThan1000 ? '0px' : '165px']}
            flexDir={['column', 'column', 'row']}
          >
            <Header
              title="Selecionar produtos"
              handleVoltar={handleVoltar}
              icon={IconeEtapaSelecaoProdutos}
              width="fit-content"
            />
          </Flex>
        )}
        <Flex align="center" w={isSmallerThan1000 ? 'fit-content' : '300px'}>
          {passoSelecionarProdutoQuandoTelaEstaMenor700 &&
            !isSmallerThan1000 && (
              <Text fontSize="14px" fontWeight="light" mr="32px">
                Total de produtos selecionados:
                <Text as="span" textDecoration="underline" ml="4px">
                  {totalProdutosSelecionados || 0}
                </Text>
              </Text>
            )}
          <Button onClick={handleSair} variant="linkDefault" colorScheme="gray">
            <Icon boxSize="20px" as={FiX} />
          </Button>
        </Flex>
      </Flex>

      <Box
        w={
          passoSelecionarProdutoQuandoTelaEstaMenor700
            ? '100%'
            : ['92%', '92%', 'full']
        }
      >
        {children}
      </Box>
    </Flex>
  );
};
