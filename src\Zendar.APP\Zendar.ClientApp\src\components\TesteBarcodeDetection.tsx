import React, { useState, useCallback } from 'react';
import {
  Box,
  Input,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  HStack,
} from '@chakra-ui/react';
import { useBarcodeDetection } from 'hooks/useBarcodeDetection';
import { useKeyboardBarcodeDetection } from 'hooks/useKeyboardBarcodeDetection';

/**
 * Componente para testar a detecção de código de barras
 * Use este componente para verificar se o leitor está funcionando corretamente
 */
export const TesteBarcodeDetection: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [detectionHistory, setDetectionHistory] = useState<
    Array<{
      value: string;
      isFromScanner: boolean;
      method: 'input' | 'keyboard';
      timestamp: Date;
    }>
  >([]);

  // Hook melhorado para detecção de código de barras
  const { detectBarcodeInput, resetDetection } = useBarcodeDetection({
    minLength: 8,
    maxTimeBetweenChars: 50,
    minSpeed: 10,
    instantInputThreshold: 100,
  });

  // Hook para detecção por eventos de teclado
  const handleKeyboardBarcode = useCallback(
    (result: { isFromScanner: boolean; scannedValue: string }) => {
      if (result.isFromScanner && result.scannedValue.length >= 8) {
        setInputValue(result.scannedValue);
        setDetectionHistory((prev) => [
          {
            value: result.scannedValue,
            isFromScanner: true,
            method: 'keyboard',
            timestamp: new Date(),
          },
          ...prev.slice(0, 9), // Mantém apenas os últimos 10
        ]);
      }
    },
    []
  );

  useKeyboardBarcodeDetection(handleKeyboardBarcode, {
    minLength: 8,
    maxTimeBetweenKeys: 100,
    endKeys: ['Enter', 'Tab'],
    enabled: true,
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputValue(value);

    if (value.length >= 8) {
      const result = detectBarcodeInput(value);
      
      if (result.isFromScanner) {
        setDetectionHistory((prev) => [
          {
            value,
            isFromScanner: true,
            method: 'input',
            timestamp: new Date(),
          },
          ...prev.slice(0, 9),
        ]);
      }
    }
  };

  const handleClear = () => {
    setInputValue('');
    resetDetection();
  };

  const handleClearHistory = () => {
    setDetectionHistory([]);
  };

  const lastDetection = detectionHistory[0];

  return (
    <VStack spacing={6} align="stretch" maxW="600px" mx="auto" p={6}>
      <Box>
        <Text fontSize="xl" fontWeight="bold" mb={4}>
          🔍 Teste de Detecção de Código de Barras
        </Text>
        <Text fontSize="sm" color="gray.600" mb={4}>
          Use este componente para testar se o leitor de código de barras está
          sendo detectado corretamente. Experimente digitar manualmente e usar
          o leitor.
        </Text>
      </Box>

      <Box>
        <Text fontSize="md" fontWeight="semibold" mb={2}>
          Campo de Teste:
        </Text>
        <Input
          value={inputValue}
          onChange={handleInputChange}
          placeholder="Digite ou escaneie um código de barras (mín. 8 caracteres)"
          size="lg"
          borderColor={lastDetection?.isFromScanner ? 'green.500' : 'gray.200'}
          borderWidth={lastDetection?.isFromScanner ? '2px' : '1px'}
          bg={lastDetection?.isFromScanner ? 'green.50' : 'white'}
        />
        <HStack mt={2}>
          <Button size="sm" onClick={handleClear} variant="outline">
            Limpar Campo
          </Button>
          <Button size="sm" onClick={handleClearHistory} variant="outline">
            Limpar Histórico
          </Button>
        </HStack>
      </Box>

      {lastDetection && (
        <Alert
          status={lastDetection.isFromScanner ? 'success' : 'info'}
          borderRadius="md"
        >
          <AlertIcon />
          <Box>
            <AlertTitle>
              {lastDetection.isFromScanner
                ? '✅ Leitor Detectado!'
                : '⌨️ Digitação Manual'}
            </AlertTitle>
            <AlertDescription>
              Método: {lastDetection.method === 'keyboard' ? 'Teclado' : 'Input'} |
              Valor: {lastDetection.value} |
              Hora: {lastDetection.timestamp.toLocaleTimeString()}
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {detectionHistory.length > 0 && (
        <Box>
          <Text fontSize="md" fontWeight="semibold" mb={3}>
            📋 Histórico de Detecções:
          </Text>
          <VStack spacing={2} align="stretch">
            {detectionHistory.map((detection, index) => (
              <Box
                key={index}
                p={3}
                borderRadius="md"
                bg={detection.isFromScanner ? 'green.50' : 'gray.50'}
                border="1px"
                borderColor={detection.isFromScanner ? 'green.200' : 'gray.200'}
              >
                <HStack justify="space-between" align="center">
                  <VStack align="start" spacing={1}>
                    <HStack>
                      <Badge
                        colorScheme={detection.isFromScanner ? 'green' : 'gray'}
                      >
                        {detection.isFromScanner ? 'LEITOR' : 'MANUAL'}
                      </Badge>
                      <Badge variant="outline">
                        {detection.method.toUpperCase()}
                      </Badge>
                    </HStack>
                    <Text fontSize="sm" fontFamily="mono">
                      {detection.value}
                    </Text>
                  </VStack>
                  <Text fontSize="xs" color="gray.500">
                    {detection.timestamp.toLocaleTimeString()}
                  </Text>
                </HStack>
              </Box>
            ))}
          </VStack>
        </Box>
      )}

      <Box bg="blue.50" p={4} borderRadius="md" border="1px" borderColor="blue.200">
        <Text fontSize="sm" color="blue.800">
          <strong>💡 Dicas para Teste:</strong>
          <br />
          • <strong>Leitor USB:</strong> Deve detectar automaticamente quando você escanear
          <br />
          • <strong>Digitação Manual:</strong> Digite devagar para simular entrada manual
          <br />
          • <strong>Códigos Curtos:</strong> Códigos com menos de 8 caracteres não são detectados
          <br />
          • <strong>Múltiplos Métodos:</strong> O sistema usa detecção por input E por teclado
        </Text>
      </Box>
    </VStack>
  );
};
