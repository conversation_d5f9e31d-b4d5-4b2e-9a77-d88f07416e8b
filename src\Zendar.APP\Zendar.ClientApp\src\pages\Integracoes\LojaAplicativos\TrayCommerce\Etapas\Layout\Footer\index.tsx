import { Button } from '@chakra-ui/react';
import React from 'react';

import { useTrayEtapasContext } from 'store/Tray';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';

export const Footer = () => {
  const { activeStep, ref } = useTrayEtapasContext();

  const isTabelaPreco =
    activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.CADASTRAR_TABELA_PRECO;

  const isBuscarProduto =
    activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.BUSCAR_PRODUTO;
  const estaNoPassoGuiaIntegracao =
    activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.GUIA_INTEGRACAO;

  const labelButton = () => {
    switch (activeStep) {
      case ETAPA_GUIA_INTEGRACAO_TRAY.GUIA_INTEGRACAO:
        return 'Iniciar Guia de Integração';
      case ETAPA_GUIA_INTEGRACAO_TRAY.IMPORTANDO:
        return 'Sair do guia de integração';
      case ETAPA_GUIA_INTEGRACAO_TRAY.CADASTRAR_TABELA_PRECO:
        return 'Criar tabela de preços';
      case ETAPA_GUIA_INTEGRACAO_TRAY.ENVIANDO_ZENDAR:
        return 'Sair do guia de integração';
      case ETAPA_GUIA_INTEGRACAO_TRAY.BUSCAR_PRODUTO:
        return 'Pesquisar produtos';

      default:
        return 'Avançar';
    }
  };

  return (
    <>
      {ref?.current?.handleAvancar && (
        <>
          {isTabelaPreco || isBuscarProduto ? (
            <Button
              w="240px"
              variant="solid"
              colorScheme="aquamarine.600"
              onClick={() => {
                if (ref?.current?.handleAvancar) {
                  ref?.current?.handleAvancar();
                }
              }}
            >
              {labelButton()}
            </Button>
          ) : (
            <Button
              variant="solid"
              colorScheme="purple"
              height="40px"
              minW={estaNoPassoGuiaIntegracao ? '240px' : '120px'}
              onClick={() => {
                if (ref?.current?.handleAvancar) {
                  ref?.current?.handleAvancar();
                }
              }}
            >
              {labelButton()}
            </Button>
          )}
        </>
      )}
    </>
  );
};
