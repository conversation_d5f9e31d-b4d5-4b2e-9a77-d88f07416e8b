import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';

import { GuiaIntegracao } from '../01_GuiaIntegracao';
import { PortalCliente } from '../02_PortalCliente';
import { Autenticacao } from '../03_Autenticacao';
import { CanalVendas } from '../04_CanalVendas';
import { Vendedores } from '../05_Vendedor';
import { LocalEstoque } from '../06_LocalEstoque';
import { TabelaPreco } from '../07_TabelaPrecos';
import { CadastrarTabelaPreco } from '../08_CadastrarTabelaPreco';
import { TipoCadastro } from '../09_Cadastros';
import { BuscarProduto } from '../10_BuscarProdutos';
import { ProdutoSistema } from '../11_ListaProdutos';
import { Importando } from '../12_Importando';

type PassosIntegracaoProps = {
  passoAtual: number;
};

export const PassoAPasso = ({ passoAtual }: PassosIntegracaoProps) => {
  switch (passoAtual) {
    case ETAPA_GUIA_INTEGRACAO_TRAY.GUIA_INTEGRACAO:
      return <GuiaIntegracao />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.PORTAL_CLIENTE:
      return <PortalCliente />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.AUTENTICACAO:
      return <Autenticacao />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.CANAL_VENDAS:
      return <CanalVendas />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.VENDEDOR:
      return <Vendedores />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.LOCAL_ESTOQUE:
      return <LocalEstoque />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.TABELA_PRECO:
      return <TabelaPreco />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.CADASTRAR_TABELA_PRECO:
      return <CadastrarTabelaPreco />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.TIPO_CADASTRO:
      return <TipoCadastro />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.BUSCAR_PRODUTO:
      return <BuscarProduto />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.IMPORTANDO:
      return <Importando />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.ENVIANDO_ZENDAR:
      return <Importando />;
    case ETAPA_GUIA_INTEGRACAO_TRAY.LISTA_PRODUTO_SISTEMA:
      return <ProdutoSistema />;

    default:
      return null;
  }
};
