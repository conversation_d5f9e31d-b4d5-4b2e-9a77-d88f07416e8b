import { VStack, Text, Box, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { copiarTabelaPreco } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import Input from 'components/PDV/Input';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import { TabelaPrecosIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { yupResolver, FormData } from './validationForms';

type Options = {
  label: string;
  value: string;
};

export type OptionResponseProps = {
  id: string;
  nome: string;
};

export const CadastrarTabelaPreco = () => {
  const [tabelaPreco, setTabelaPreco] = useState<Options[]>([]);

  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: {
      tabelaPreco: null,
      tabelaPrecoNome: '',
    },
  });

  const { handleSubmit: onSubmit, watch } = formMethods;

  const tabelaPrecoSelecionada = watch('tabelaPreco');
  const tabelaPrecoNome = watch('tabelaPrecoNome');

  const handleAvancar = useCallback(() => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.MIDIAS_SOCIAIS);
  }, [setPassoAtual]);

  const handleVoltar = useCallback(() => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.TABELA_PRECO);
  }, [setPassoAtual]);

  const obterTabelasPreco = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionResponseProps[]>>(
      ConstanteEnderecoWebservice.TABELA_PRECO_LISTAR_TABELAS_PRECO
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response.sucesso && response.dados) {
        setTabelaPreco(
          response.dados.map((tabelasPreco) => {
            return {
              label: tabelasPreco.nome,
              value: tabelasPreco.id,
            } as Options;
          })
        );
      }
    }
  }, []);

  const handleSubmit = onSubmit(async (data) => {
    setIsLoading(true);
    const response = await copiarTabelaPreco(
      data.tabelaPreco as string,
      data.tabelaPrecoNome
    );

    if (response) {
      handleAvancar();
      setIsLoading(false);
    }
    setIsLoading(false);
  });

  useEffect(() => {
    obterTabelasPreco();
  }, [obterTabelasPreco]);

  return (
    <>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Criar uma nova tabela de preços"
          handleVoltar={handleVoltar}
          icon={TabelaPrecosIcon}
        />
        <FormProvider {...formMethods}>
          <VStack
            pl={['10px', '10px', '20px']}
            pr={['10px', '10px', '20px']}
            fontSize="14px"
            w="600px"
            alignItems="left"
            spacing="24px"
          >
            <Text
              color="primary.50"
              mb="4px"
              textAlign="left"
              letterSpacing="0px"
            >
              Selecione abaixo uma tabela para servir como base de preços. A
              tabela selecionada servirá apenas como referência para a criação
              da nova tabela para o Cardápio.
            </Text>
            <Box color="primary.50">
              <Text mb="20px" textAlign="left" letterSpacing="0px">
                Todos os produtos selecionados para venda no cardápio serão
                adicionados automaticamente nessa tabela.
              </Text>
              <Text mb="4px" textAlign="left" letterSpacing="0px">
                Para informar um preço específico para o produto que será
                vendido no cardápio:
              </Text>
              <Text color="primary.50" ml="20px">
                - No cadastro do produto, na aba “preços”, selecione a nova
                tabela;
              </Text>
              <Text color="primary.50" ml="20px">
                - Acesse diretamente a nova tabela, no menu Configurações {'>'}{' '}
                Tabela de preços.
              </Text>
            </Box>

            <Input
              id="tabelaPrecoNome"
              name="tabelaPrecoNome"
              label="Nome"
              placeholder="Digite o nome da tabela de preços"
              colSpan={12}
              maxLength={60}
              border="1px solid"
              borderColor="gray.200"
              fontWeightLabel="normal"
              _placeholder={{ color: '#BBBBBB' }}
              height="40px"
            />
            <SelectPadrao
              id="tabelaPreco"
              name="tabelaPreco"
              label="Informe a tabela que será utilizada como referência:"
              fontWeightLabel="normal"
              styles={{
                control: () => ({
                  minHeight: '40px !important',
                }),
              }}
              asControlledByObject={false}
              colSpan={12}
              options={tabelaPreco}
              placeholder="Selecione a tabela de preço"
            />
          </VStack>
        </FormProvider>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleSubmit}
        isDisabledAvancar={!tabelaPrecoSelecionada || !tabelaPrecoNome}
      />
    </>
  );
};
