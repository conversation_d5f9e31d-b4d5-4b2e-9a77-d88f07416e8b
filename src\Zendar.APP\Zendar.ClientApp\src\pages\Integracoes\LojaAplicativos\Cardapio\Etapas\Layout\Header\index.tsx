import { Flex, Text, Icon, Box, useMediaQuery } from '@chakra-ui/react';
import { useImperativeHandle } from 'react';

import { useEtapasContext } from 'store/Cardapio/Etapas/EtapasContext';

import { IconType } from 'icons/types';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

type HeaderProps = {
  icon: IconType;
  iconSize?: number;
  title: string;
  handleVoltar?: () => void;
  colorIcon?: string;
  width?: string | Array<string>;
};

export const Header = ({
  icon,
  title,
  iconSize = 45,
  handleVoltar,
  colorIcon = 'primary.50',
  width = ['full', '600px', '600px'],
}: HeaderProps) => {
  const { passoAtual, refHandleVoltar } = useEtapasContext();
  const passoGuiaIntegracao =
    passoAtual === ETAPA_GUIA_INTEGRACAO_CARDAPIO.INICIO;

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const passoSelecionarProdutoQuandoTelaEstaMenor700 =
    passoAtual === ETAPA_GUIA_INTEGRACAO_CARDAPIO.SELECIONAR_PRODUTOS &&
    isLargerThan700;
  useImperativeHandle(refHandleVoltar, () => ({
    handle: handleVoltar,
  }));

  return (
    <Box
      w={width}
      borderBottomStyle="solid"
      borderBottomWidth={
        passoSelecionarProdutoQuandoTelaEstaMenor700 ? '0px' : '1px'
      }
      borderBottomColor="gray.200"
      mb="24px"
    >
      <Flex
        flexDir={
          passoSelecionarProdutoQuandoTelaEstaMenor700
            ? ['column', 'row']
            : 'column'
        }
        gap={
          passoSelecionarProdutoQuandoTelaEstaMenor700 ? ['4px', '12px'] : '0px'
        }
        align="center"
      >
        <Flex
          justifyContent="center"
          w={
            passoSelecionarProdutoQuandoTelaEstaMenor700
              ? 'fit-content'
              : 'full'
          }
          alignItems="center"
          cursor="pointer"
        >
          <Icon
            boxSize={`${iconSize}px`}
            size={iconSize}
            color={colorIcon}
            as={icon}
          />
        </Flex>

        <Flex
          mb={passoSelecionarProdutoQuandoTelaEstaMenor700 ? '0px' : '12px'}
          w="full"
          alignItems={
            passoSelecionarProdutoQuandoTelaEstaMenor700 ? 'center' : 'baseline'
          }
          justifyContent={passoGuiaIntegracao ? 'space-between' : 'center'}
        >
          <Box w="full">
            <Text
              textAlign={
                passoSelecionarProdutoQuandoTelaEstaMenor700
                  ? 'start'
                  : 'center'
              }
              fontSize={
                passoSelecionarProdutoQuandoTelaEstaMenor700
                  ? ['16px', '24px']
                  : ['20px', '20px', '28px']
              }
              fontWeight="bold"
              marginTop={['8px', '8px', '8px']}
            >
              {title}
            </Text>
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
};
