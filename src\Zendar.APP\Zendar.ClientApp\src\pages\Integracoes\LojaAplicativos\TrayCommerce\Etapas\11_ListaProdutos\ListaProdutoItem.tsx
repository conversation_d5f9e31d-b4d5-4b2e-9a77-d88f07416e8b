import { Box, Flex, Icon, Text, Checkbox } from '@chakra-ui/react';

import { ProdutoSemCadastroTrayProps } from 'api/Integrações/Tray';
import { enumPossuiVariacao } from 'constants/enum/enumPossuiVariacao';
import { GradeTamanhosIcon } from 'icons';

type ListaProdutoItemProps = {
  produto: ProdutoSemCadastroTrayProps;
  handleAlternarSelecionarProduto: (item: ProdutoSemCadastroTrayProps) => void;
  handleAbrirVariacoes: (produto: ProdutoSemCadastroTrayProps) => Promise<void>;
};

export const ListaProdutoItem = ({
  produto,
  handleAlternarSelecionarProduto,
  handleAbrirVariacoes,
}: ListaProdutoItemProps) => {
  const produtoComVariacao =
    produto.tipoProduto === enumPossuiVariacao.VARIACAO;

  return (
    <Flex
      h="48px"
      bg={produto.estaSelecionado ? 'purple.50' : 'white'}
      pl="20px"
      mb="4px"
      pr={{ base: '10px', md: '24px' }}
      color="black"
      alignItems="center"
      cursor="pointer"
      justifyContent="space-between"
      borderRadius="5px"
      boxShadow="0px 0px 4px #00000029"
      onClick={(e) => {
        e.stopPropagation();
        handleAlternarSelecionarProduto(produto);
      }}
    >
      <Flex justifyContent="center" alignItems="center">
        <Checkbox
          mb="0"
          isChecked={produto.estaSelecionado}
          colorScheme="primary"
          pointerEvents="none"
        />
        <Box>
          <Text fontSize="14px" mt="3px" ml="10px" color="gray.700">
            {produto.nome}
          </Text>
        </Box>
      </Flex>
      {produtoComVariacao && (
        <Flex
          justifyContent="center"
          w="24px"
          h="24px"
          p="5px"
          borderRadius="5px"
          alignItems="center"
          cursor="pointer"
          _hover={{ bg: 'gray.100' }}
          _active={{ bg: 'gray.100' }}
          onClick={(e) => {
            e.stopPropagation();
            handleAbrirVariacoes(produto);
          }}
          borderColor="gray.600"
          borderWidth="1px"
        >
          <Icon as={GradeTamanhosIcon} fontSize="15px" />
        </Flex>
      )}
    </Flex>
  );
};
