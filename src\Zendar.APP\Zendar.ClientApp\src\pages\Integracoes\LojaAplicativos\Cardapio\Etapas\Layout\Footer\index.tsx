import { Flex, HStack, Button, FlexProps } from '@chakra-ui/react';

import { useEtapasContext } from 'store/Cardapio/Etapas/EtapasContext';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

interface FooterProps extends FlexProps {
  handleClienteStone?: () => void;
  handleAvancar: () => void;
  handleSair?: () => void;
  isDisabledAvancar?: boolean;
}

export const Footer = ({
  handleAvancar,
  handleSair,
  isDisabledAvancar,
  ...rest
}: FooterProps) => {
  const { passoAtual } = useEtapasContext();

  const exibirButtons = () => {
    switch (passoAtual) {
      case ETAPA_GUIA_INTEGRACAO_CARDAPIO.INICIO:
        return (
          <HStack
            flexDirection={['column', 'column', 'row']}
            w="full"
            justifyContent="center"
            spacing={['0', '0', '24px']}
          >
            <Button
              mt={['24px', '24px', '0']}
              onClick={handleAvancar}
              w="240px"
              height="40px"
              variant="solid"
              fontWeight="normal"
              colorScheme={isDisabledAvancar ? 'blackAlpha' : 'purple'}
              isDisabled={isDisabledAvancar}
            >
              Iniciar Guia de Integração
            </Button>
          </HStack>
        );
      case ETAPA_GUIA_INTEGRACAO_CARDAPIO.CADASTRAR_TABELA_PRECO:
        return (
          <Button
            w="240px"
            onClick={handleAvancar}
            colorScheme="aquamarine"
            isDisabled={isDisabledAvancar}
            variant="solid"
            h="40px"
            fontWeight="normal"
          >
            Criar tabela de preços
          </Button>
        );

      default:
        return (
          <Button
            w="200px"
            height="40px"
            variant="solid"
            fontWeight="normal"
            colorScheme={isDisabledAvancar ? 'blackAlpha' : 'purple'}
            onClick={handleAvancar}
            isDisabled={isDisabledAvancar}
          >
            Avançar
          </Button>
        );
    }
  };

  return (
    <Flex
      bg="gray.50"
      flexDirection={['column', 'column', 'row']}
      justifyContent="center"
      w="full"
      alignItems="center"
      {...rest}
    >
      {exibirButtons()}
    </Flex>
  );
};
