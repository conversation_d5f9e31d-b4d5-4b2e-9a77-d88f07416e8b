import {
  ModalProps,
  ModalContent,
  ModalBody,
  Button,
  useDisclosure,
  Text,
  Icon,
  Flex,
  ModalFooter,
  Box,
  ModalHeader,
  useMediaQuery,
} from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { FiTrash2 } from 'react-icons/fi';
import { create, InstanceProps } from 'react-modal-promise';
import { ListRowProps } from 'react-virtualized';

import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';

import { useProdutosStore } from '../../produtos';
import { VirtualizadaSemBuscarItens } from '../Listagem';

type ProdutoSelecionado = {
  produtoId: string;
  listaProdutoCorTamanhoId: string[];
  descricao: string;
};

type ModalProdutosSelecionadosProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<ModalProps> & {
    listProdutos: ProdutoSelecionado[];
    reload: () => void;
    handleEnviarProdutos?: () => void;
  };

export const ModalProdutosSelecionados = create<
  ModalProdutosSelecionadosProps,
  ModalProps
>(
  ({
    onResolve,
    onReject,
    reload,
    listProdutos: produtos,
    handleEnviarProdutos,
    ...rest
  }) => {
    const [listProdutos, setListProdutos] = useState(produtos);

    const { isOpen, onClose: handleClose } = useDisclosure({
      defaultIsOpen: true,
    });

    const remover = useProdutosStore((s) => s.remover);

    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

    const onClose = () => {
      handleClose();
    };

    const handleRemoverProduto = useCallback(
      (id: string) => {
        remover(id);
        setListProdutos((prev) =>
          prev.filter((produto) => produto.produtoId !== id)
        );
      },
      [remover]
    );

    const handleConfirmar = () => {
      if (handleEnviarProdutos) handleEnviarProdutos();
      handleClose();
    };

    return (
      <ModalPadraoChakra
        isCentered={isLargerThan900}
        size={isLargerThan900 ? '5xl' : 'full'}
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent
          bg="gray.50"
          borderRadius={isLargerThan900 ? 'md' : '0'}
          w={isLargerThan900 ? '880px' : 'full'}
          h={isLargerThan900 ? '580px' : 'full'}
        >
          <ModalHeader
            display="flex"
            justifyContent="space-between"
            px="40px"
            pt="24px"
            pb="12px"
            alignItems="center"
          >
            <Text color="primary.50" fontSize="20px">
              Produtos selecionados
            </Text>
            <Text fontSize="14px">
              {`${listProdutos.length}${
                listProdutos.length === 1
                  ? ' item selecionado'
                  : ' itens selecionados'
              }`}
            </Text>
          </ModalHeader>

          <ModalBody pl="40px" pr="28px" pt="0" pb="0">
            <VirtualizadaSemBuscarItens
              rowCount={listProdutos.length}
              rowHeight={48}
              rowRenderer={({ index }: ListRowProps) => {
                const produtoItem = listProdutos[index];
                return (
                  <>
                    <Flex
                      h="48px"
                      borderRadius="5px"
                      boxShadow="0px 0px 4px #00000029"
                      bg="white"
                      px="24px"
                      alignItems="center"
                      justifyContent="space-between"
                      _hover={{
                        boxShadow: '0px 0px 4px rgba(85, 2, 178, 0.5)',
                      }}
                    >
                      <Text color="black">{produtoItem.descricao}</Text>
                      <Icon
                        onClick={() =>
                          handleRemoverProduto(produtoItem.produtoId)
                        }
                        cursor="pointer"
                        boxSize="17px"
                        color="gray.700"
                        as={FiTrash2}
                      />
                    </Flex>
                    <Box h="4px" />
                  </>
                );
              }}
            />
          </ModalBody>
          <ModalFooter flexDirection="column" mb="40px">
            <Flex
              w="full"
              h="full"
              justifyContent="center"
              alignItems="baseline"
            >
              <Button
                variant="outlineDefault"
                colorScheme="gray"
                lineHeight="0"
                borderRadius="20px"
                fontSize="16px"
                fontWeight="normal"
                h="40px"
                width="120px"
                mr="24px"
                onClick={onClose}
              >
                Fechar
              </Button>
              {handleEnviarProdutos && (
                <Button
                  color="white"
                  variant="solid"
                  colorScheme="purple"
                  fontWeight="normal"
                  lineHeight="0"
                  borderRadius="20px"
                  fontSize="16px"
                  h="40px"
                  width="250px"
                  isDisabled={listProdutos.length === 0}
                  onClick={() => handleConfirmar()}
                >
                  Confirmar e enviar produtos
                </Button>
              )}
            </Flex>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
