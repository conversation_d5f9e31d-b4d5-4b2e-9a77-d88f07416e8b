import {
  createContext,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useHistory } from 'react-router-dom';

import api, { ResponseApi } from 'services/api';
import { obterEtapaAtual } from 'services/tray';

import { useProdutosStore } from 'pages/Integracoes/LojaAplicativos/TrayCommerce/Etapas/produtos';
import { ProdutoSelecionado } from 'pages/Integracoes/LojaAplicativos/TrayCommerce/Etapas/types';

import { obterDadosIntegracaoTray } from 'api/Tray/ObterDadosIntegração';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumIdentificacaoIntegracao } from 'constants/enum/enumIdentificacaoIntegracao';
import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import ConstanteRotas from 'constants/rotas';

type RefHandlerProps = {
  handleVoltar?: () => void;
  handleAvancar?: () => void;
  handleConfirmarProdutoSelecionado?: () => void;
  handleExibirProdutosSalvos?: () => void;
};

type TrayEtapasProps = {
  activeStep: number;
  setActiveStep: React.Dispatch<SetStateAction<number>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  ref: React.RefObject<RefHandlerProps>;
  setTotalProdutosListados: React.Dispatch<SetStateAction<number>>;
  totalProdutoListados: number;
  setTemProdutoSelecionadoListagem: React.Dispatch<SetStateAction<boolean>>;
  temProdutoSelecionadoListagem: boolean;
  setIsOpenBuscaAvancada: React.Dispatch<SetStateAction<boolean>>;
  isOpenBuscaAvancada: boolean;
  setHasFiltros: React.Dispatch<SetStateAction<boolean>>;
  hasFiltros: boolean;
  setIsProdutoSistema: React.Dispatch<SetStateAction<boolean>>;
  isProdutoSistema: boolean;
  produtosSelecionadosSalvos: ProdutoSelecionado[];
  limparProdutosSalvos: () => void;
  adicionarVarios: (
    novos: ProdutoSelecionado[],
    salvos?: ProdutoSelecionado[]
  ) => void;
  dadosTray: () => Promise<ResponseApi<{ configuracoes: string }>>;
};

type TrayEtapasProviderProps = {
  children: ReactNode;
};

export const TrayEtapasContext = createContext({} as TrayEtapasProps);

export const TrayEtapasProvider = ({ children }: TrayEtapasProviderProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [totalProdutoListados, setTotalProdutosListados] = useState(0);
  const [temProdutoSelecionadoListagem, setTemProdutoSelecionadoListagem] =
    useState(false);
  const [isOpenBuscaAvancada, setIsOpenBuscaAvancada] = useState(false);
  const [isProdutoSistema, setIsProdutoSistema] = useState(false);
  const [hasFiltros, setHasFiltros] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const produtosSelecionadosSalvos = useProdutosStore((s) => s.produtos);
  const limparProdutosSalvos = useProdutosStore((s) => s.limpar);
  const adicionarVarios = useProdutosStore((s) => s.adicionarVarios);

  const ref = useRef<RefHandlerProps>(null);

  const history = useHistory();

  const cadastrarTray = useCallback(async () => {
    setIsLoading(true);

    await api.post<void, ResponseApi>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_CADASTRAR,
      {
        identificacaoIntegracao: enumIdentificacaoIntegracao.TRAY,
      }
    );

    setIsLoading(false);
  }, []);

  const alterarEtapaTray = useCallback(async () => {
    if (
      activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.GUIA_INTEGRACAO ||
      activeStep === ETAPA_GUIA_INTEGRACAO_TRAY.CADASTRAR_TABELA_PRECO
    ) {
      return;
    }

    setIsLoading(true);

    await api.put<void, ResponseApi>(
      ConstanteEnderecoWebservice.INTEGRACAO_TRAY_ALTERAR_IDENTIFICACAO_ETAPA,
      {
        identificacaoEtapas: activeStep,
      }
    );

    setIsLoading(false);
  }, [activeStep]);

  const dadosTray = useCallback(async () => {
    const response = await obterDadosIntegracaoTray();
    return response;
  }, []);

  const getTray = useCallback(async () => {
    setIsLoading(true);

    const responseDataTray = await dadosTray();

    if (responseDataTray) {
      if (responseDataTray.dados && responseDataTray.sucesso) {
        const response = await obterEtapaAtual();

        if (response !== null) {
          setActiveStep(response);
          if (response === ETAPA_GUIA_INTEGRACAO_TRAY.ETAPA_FINALIZADA) {
            history.push(ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_PAINEL_ADM);
          }
        }
      } else {
        cadastrarTray();
      }
    }

    setIsLoading(false);
  }, [cadastrarTray, dadosTray, history]);

  useEffect(() => {
    alterarEtapaTray();
  }, [alterarEtapaTray]);

  useEffect(() => {
    getTray();
  }, [getTray]);

  return (
    <TrayEtapasContext.Provider
      value={{
        activeStep,
        setActiveStep,
        temProdutoSelecionadoListagem,
        setTemProdutoSelecionadoListagem,
        isLoading,
        setIsLoading,
        produtosSelecionadosSalvos,
        limparProdutosSalvos,
        adicionarVarios,
        ref,
        setTotalProdutosListados,
        totalProdutoListados,
        dadosTray,
        isOpenBuscaAvancada,
        setIsOpenBuscaAvancada,
        hasFiltros,
        setHasFiltros,
        isProdutoSistema,
        setIsProdutoSistema,
      }}
    >
      {children}
    </TrayEtapasContext.Provider>
  );
};

export function useTrayEtapasContext(): TrayEtapasProps {
  const context = useContext(TrayEtapasContext);

  if (!context)
    throw new Error(
      'useTrayEtapasContext must be used within a TrayEtapasProvider.'
    );

  return context;
}
