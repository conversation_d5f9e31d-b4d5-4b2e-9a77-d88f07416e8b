import { Flex, FlexProps, useMediaQuery } from '@chakra-ui/react';
import React, {
  createContext,
  useState,
  useContext,
  Dispatch,
  SetStateAction,
  useRef,
  useCallback,
  useEffect,
} from 'react';
import { useHistory } from 'react-router-dom';

import {
  iniciarIntegracao,
  obterDadosIntegracao,
  obterEtapaAtual,
} from 'services/cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas from 'constants/rotas';

type RefHandleVoltarProps = {
  handle?: () => void;
};

interface EtapasContextProps {
  passoAtual: number;
  setPassoAtual: Dispatch<SetStateAction<number>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  animacaoLoading: () => void;
  refHandleVoltar: React.RefObject<RefHandleVoltarProps>;
}

export const EtapasContext = createContext<EtapasContextProps>(
  {} as EtapasContextProps
);

interface EtapasProviderProps {
  children: React.ReactNode;
}

interface ContainerLayoutProps extends FlexProps {
  children: React.ReactNode;
  height?: string;
}

export const ContainerIntegracaoFixo = ({
  children,
  height = '100vh',
  align = 'center',
}: ContainerLayoutProps) => {
  const [isLargerThan600] = useMediaQuery('(min-height: 600px)');
  return (
    <Flex
      overflowY="auto"
      mb={!isLargerThan600 ? '20px' : undefined}
      h={`calc(${!isLargerThan600 ? '100%' : height} - 151px)`}
      justify="flex-start"
      flexDirection="column"
      align={align}
    >
      {children}
    </Flex>
  );
};

export function EtapasProvider({ children }: EtapasProviderProps): JSX.Element {
  const [passoAtual, setPassoAtual] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const history = useHistory();

  const refHandleVoltar = useRef<RefHandleVoltarProps>(null);

  const animacaoLoading = useCallback(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [setIsLoading]);

  const obterEtapaIntegracao = useCallback(async () => {
    setIsLoading(true);

    const dadosIntegracao = await obterDadosIntegracao();

    if (dadosIntegracao) {
      if (dadosIntegracao.dados && dadosIntegracao.sucesso) {
        const response = await obterEtapaAtual();

        if (response !== null) {
          setPassoAtual(response);
          if (response === ETAPA_GUIA_INTEGRACAO_CARDAPIO.FINALIZADO) {
            history.push(ConstanteRotas.CARDAPIO_VENDAS_PAINEL);
          }
        }
      } else {
        iniciarIntegracao();
      }
    }

    setIsLoading(false);
  }, [iniciarIntegracao, obterDadosIntegracao, history]);

  useEffect(() => {
    obterEtapaIntegracao();
  }, [obterEtapaIntegracao]);

  return (
    <EtapasContext.Provider
      value={{
        passoAtual,
        setPassoAtual,
        isLoading,
        setIsLoading,
        animacaoLoading,
        refHandleVoltar,
      }}
    >
      {children}
    </EtapasContext.Provider>
  );
}

export function useEtapasContext(): EtapasContextProps {
  const context = useContext(EtapasContext);

  if (!context)
    throw new Error('EtapasContext must be used within a EtapasProvider.');

  return context;
}
