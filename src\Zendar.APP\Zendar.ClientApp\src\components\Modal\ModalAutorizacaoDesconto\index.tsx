import {
  ModalProps,
  ModalContent,
  ModalBody,
  Button,
  useDisclosure,
  Text,
  Flex,
  Heading,
  ScaleFade,
  useMediaQuery,
  VStack,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import validarUsuarioDeLiberacao from 'helpers/api/Operacao/validarUsuarioDeLiberacao';

import ManterFoco from 'components/Geral/ManterFoco';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import Input from 'components/PDV/Input';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';

import LogAuditoriaTelaEnum from 'constants/enum/logAuditoriaTela';
import { DesbloquearIcon } from 'icons';

import { yupResolver } from './validationForm';

type FormData = {
  usuario: string;
  senha: string;
};

type ResolveModalProps = {
  id: string;
  nome: string;
  descontoMaximo: number;
};

type ModalAutorizacaoDescontoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<ResolveModalProps | null>;

export const ModalAutorizacaoDesconto = create<ModalAutorizacaoDescontoProps>(
  ({ onResolve, onReject, ...rest }) => {
    const [isLargerThan900] = useMediaQuery('(min-width: 900px');
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const [isLoading, setIsLoading] = useState(false);

    const formMethods = useForm<FormData>({
      resolver: yupResolver,
      defaultValues: {
        usuario: '',
        senha: '',
      },
    });

    const handleSubmit = formMethods.handleSubmit(async (data) => {
      setIsLoading(true);

      const { usuario, senha } = data;

      const response = await validarUsuarioDeLiberacao({
        usuario,
        senha,
        tela: LogAuditoriaTelaEnum.PDV,
      });

      if (response?.avisos) {
        response.avisos.map((aviso) => toast.warning(aviso));
      }

      setIsLoading(false);
      onResolve(response?.dados);
      onClose();
    });

    return (
      <ModalPadraoChakra
        size={isLargerThan900 ? '2xl' : 'full'}
        isCentered={isLargerThan900}
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent
          marginBottom={{ base: 0, sm: '3.75rem' }}
          marginTop={{ base: 0, sm: '3.75rem' }}
          h={['full', 'full', '520px']}
          bg="gray.50"
          maxW={['full', 'full', '600px']}
        >
          <ManterFoco
            style={{
              width: '100%',
              height: '100%',
            }}
          >
            <ScaleFade initialScale={1.5} in={isOpen}>
              <ModalBody mb="30px" p={0} h="100%">
                <FormProvider {...formMethods}>
                  {isLoading && <LoadingPadrao />}
                  <Flex flexDirection="column" align="stretch" h="100%">
                    <Flex
                      justifyContent="center"
                      h="135px"
                      bg="primary.50"
                      style={{
                        borderTopLeftRadius: '5px',
                        borderTopRightRadius: '5px',
                      }}
                    >
                      <DesbloquearIcon
                        style={{
                          fontSize: '75px',
                          fontWeight: 'bolder',
                          marginTop: '35px',
                          color: 'white',
                        }}
                      />
                    </Flex>

                    <Flex
                      h="366px"
                      justifyContent="flex-start"
                      flexDirection="column"
                      bg="gray.50"
                      px={8}
                      py={7}
                    >
                      <Heading
                        as="h2"
                        fontSize="2xl"
                        color="primary.50"
                        fontWeight="semibold"
                      >
                        Autorizar desconto acima do limite
                      </Heading>
                      <Text mt={15} fontSize="sm" color="gray.700">
                        Insira os dados de um administrador ou de um usuário com
                        permissão.
                      </Text>
                      <Flex
                        flexDirection="column"
                        gap="12px"
                        mt={6}
                        px={['10px', '10px', '24']}
                        w="full"
                      >
                        <Input
                          id="usuario"
                          name="usuario"
                          label="Usuário ou e-mail"
                          maxLength={256}
                          placeholder="Usuário"
                        />

                        <Input
                          name="senha"
                          maxLength={50}
                          label="Senha"
                          placeholder="Senha"
                          isPassword
                          inputRightElement
                        />

                        <Button
                          variant="solid"
                          isLoading={isLoading}
                          colorScheme="secondary"
                          w="full"
                          onClick={handleSubmit}
                          mt="12px"
                        >
                          Confirmar
                        </Button>
                        <Button
                          variant="outlineDefault"
                          isLoading={isLoading}
                          colorScheme="gray"
                          w="full"
                          onClick={() => onClose()}
                        >
                          Cancelar
                        </Button>
                      </Flex>
                    </Flex>
                  </Flex>
                </FormProvider>
              </ModalBody>
            </ScaleFade>
          </ManterFoco>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
