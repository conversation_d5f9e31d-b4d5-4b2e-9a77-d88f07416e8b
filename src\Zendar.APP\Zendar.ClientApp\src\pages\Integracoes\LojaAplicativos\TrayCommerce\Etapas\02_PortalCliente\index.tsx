import { VStack, Text, Box, Link } from '@chakra-ui/react';
import { useCallback, useImperativeHandle } from 'react';

import auth from 'modules/auth';

import { capitalize } from 'helpers/format/stringFormats';

import { ContainerIntegracaoFixo } from 'store/Cardapio/Etapas/EtapasContext';
import { useTrayEtapasContext } from 'store/Tray';

import { InfoTooltip } from 'components/update/Tooltip/InfoTooltip';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { TrayIconPortal } from 'icons';

import { Header } from '../Layout/Header';

export const PortalCliente = () => {
  const { name: nomeSistema } = auth.getSistema();
  const { ref, setActiveStep } = useTrayEtapasContext();

  const handleAvancar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.AUTENTICACAO);
  }, [setActiveStep]);

  const handleVoltar = () => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.GUIA_INTEGRACAO);
  };

  useImperativeHandle(ref, () => ({
    handleAvancar,
    handleVoltar,
  }));

  return (
    <ContainerIntegracaoFixo>
      <Header
        title="Portal do cliente"
        handleVoltar={handleVoltar}
        icon={TrayIconPortal}
      />
      <VStack
        pl={['10px', '10px', '20px']}
        pr={['10px', '10px', '20px']}
        color="purple.500"
        fontSize="14px"
        fontWeight="normal"
        spacing="24px"
        maxW="600px"
      >
        <Text textAlign="left" w="full" letterSpacing="0px">
          Siga as etapas abaixo para autorizar a comunicação da Tray com o
          sistema {capitalize(nomeSistema)}:
        </Text>
        <Box>
          <Text mb="4px" textAlign="left" letterSpacing="0px" pl="36px">
            <span
              style={{
                fontWeight: 'bold',
                marginRight: '3px',
                color: 'purple.500',
              }}
            >
              1)
            </span>{' '}
            Acesse o{' '}
            <Link
              href="https://www.tray.com.br/"
              target="_blank"
              style={{
                textDecoration: 'underline',
                cursor: 'pointer',
              }}
              _hover={{ color: 'purple.400' }}
            >
              portal do cliente Tray;
            </Link>
          </Text>
          <Text mb="4px" textAlign="left" letterSpacing="0px" pl="36px">
            <span style={{ fontWeight: 'bold', marginRight: '3px' }}>2)</span>{' '}
            No menu lateral do portal Tray, selecione a opção “Meus
            aplicativos”;
          </Text>
          <Text mb="4px" textAlign="left" letterSpacing="0px" pl="36px">
            <span style={{ fontWeight: 'bold', marginRight: '3px' }}>3)</span>{' '}
            Clique no botão “Instalar novos aplicativos”;
          </Text>
          <Text mb="4px" textAlign="left" letterSpacing="0px" pl="36px">
            <span style={{ fontWeight: 'bold', marginRight: '3px' }}>4)</span>{' '}
            Digite PowerStock Cloud no campo “nome do aplicativo” e clique no
            botão “filtro”;
          </Text>
          <Text mb="4px" textAlign="left" letterSpacing="0px" pl="36px">
            <span style={{ fontWeight: 'bold', marginRight: '3px' }}>5)</span>Ao
            aparecer o logo do app PowerStock Cloud, clique em “Instalar
            aplicativo”;
          </Text>
          <Text
            mb="4px"
            textAlign="left"
            display="inline"
            letterSpacing="0px"
            pl="36px"
          >
            <span style={{ fontWeight: 'bold', marginRight: '3px' }}>6)</span>{' '}
            Clique em AUTORIZAR para que o aplicativo tenha permissão de acesso
            e comunicação com o seu portal da Tray;
            <span style={{ display: 'inline-block', marginLeft: '5px' }}>
              <InfoTooltip
                bgColor="#0F0F0F"
                placement="auto-start"
                fontSize="12px"
                letterSpacing="0px"
                padding="18px 24px 22px 24px"
                maxW="450px"
                borderRadius="3px"
                label="Em alguns casos a Tray pode apresentar uma tela solicitando nome de usuário e senha. Para esta situação simplesmente ignore e feche a tela. Trata-se de uma solicitação equivocada. Se a tela não apareceu para você, siga adiante normalmente e verifique a instalação conforme o passo 7."
              />
            </span>
          </Text>
          <Text mb="4px" textAlign="left" letterSpacing="0px">
            <span
              style={{
                fontWeight: 'bold',
                marginRight: '3px',
                paddingLeft: '36px',
              }}
            >
              7)
            </span>{' '}
            Pronto! Para verificar se o aplicativo foi autorizado, clique
            novamente no menu lateral do Portal Tray e selecione “meus
            aplicativos”. Se o app estiver na sua lista ele foi instalado
            corretamente. Não feche o portal, vamos utilizá-lo novamente a
            seguir.
          </Text>
        </Box>

        <Text w="full" textAlign="left" letterSpacing="0px">
          Se todos os itens foram confirmados, clique em “avançar” para seguir
          com a integração.
        </Text>
      </VStack>
    </ContainerIntegracaoFixo>
  );
};
