import {
  Text,
  VStack,
  Box,
  useMedia<PERSON>uery,
  HStack,
  Flex,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';

import { converterArquivoParaJPGE } from 'helpers/format/converterArquivoParaJPGE';

import { atualizarLogoDelivery, obterLogoSalva } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { IconeEtapaImagemLogo } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { ImagemProps, Logo } from './components/Logo';

export const ImagemParaLogo = () => {
  const { setPassoAtual, setIsLoading, animacaoLoading } = useEtapasContext();
  const [logo, setLogo] = useState<ImagemProps>();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const handleSubmit = async () => {
    setIsLoading(true);
    const base64SemPrefixo = logo?.imagem?.split(',')[1] || '';
    const linkImagem = logo?.imagemLink || '';
    if (logo?.imagem) {
      const response = await atualizarLogoDelivery(
        linkImagem || base64SemPrefixo
      );
      if (response) {
        setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_CAPA);
      }
    }

    setIsLoading(false);
  };

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.DESCRICAO_LOJA);
  };

  const adicionarImagem = (imagem: ImagemProps) => {
    setLogo(imagem);
  };

  const handleAdicionarImagem = async (file: File) => {
    const { imagemBase64, resolucao } = await converterArquivoParaJPGE(file);
    if (imagemBase64 && resolucao) {
      const foraPadrao =
        resolucao.width > 256 || resolucao.height > 256
          ? 'resolucao'
          : file.size > 500 * 1024
          ? 'tamanho'
          : null;

      const imagem = {
        tamanho: file.size,
        imagem: imagemBase64,
        resolucao,
        foraPadrao: foraPadrao as typeof foraPadrao,
        foiRedimensionada: false,
      };

      adicionarImagem(imagem);
    }
  };

  const buscarLogoAtual = useCallback(async () => {
    setIsLoading(true);
    const response = await obterLogoSalva();
    if (response) {
      animacaoLoading();
      setLogo({
        foraPadrao: null,
        imagemLink: response,
        imagem: response,
      });
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    buscarLogoAtual();
  }, [buscarLogoAtual]);

  return (
    <>
      <ContainerIntegracaoFixo>
        <Header
          title="Imagem para Logo"
          handleVoltar={handleVoltar}
          icon={IconeEtapaImagemLogo}
        />
        <Box pl={['10px', '20px', '60px']} pr={['10px', '20px', '60px']}>
          <VStack
            mb={['0', '22px', '28px']}
            color="primary.50"
            fontSize="14px"
            justifyContent="center"
            spacing="24px"
          >
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
              whiteSpace="pre-line"
              w={isLargerThan700 ? '560px' : 'full'}
            >
              {`Personalize o site com sua marca. Ela será exibida no topo da página, ao lado do nome.
              → Utilize imagens com extensão JPG, JPEG ou PNG
              → Tamanho máximo do arquivo de 500 kb
              → Resolução recomendada: 256 x 256 px.`}
            </Text>
          </VStack>
          <HStack
            justifyContent="center"
            alignItems="center"
            flexDirection={['column', 'row', 'row']}
            spacing={['0', '16px', '16px']}
          >
            <Flex
              mt={['20px', '20px', '14px']}
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
            >
              <Box>
                <Logo
                  handleAdicionarImagem={handleAdicionarImagem}
                  imagem={logo}
                  handleRemoverImagem={() => {
                    setLogo(undefined);
                  }}
                />
              </Box>
            </Flex>
          </HStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        handleAvancar={handleSubmit}
        isDisabledAvancar={!logo || !!logo?.foraPadrao}
      />
    </>
  );
};
