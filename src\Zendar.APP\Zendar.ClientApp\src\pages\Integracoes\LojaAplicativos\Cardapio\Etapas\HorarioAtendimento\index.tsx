import { Text, VStack, Box, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import {
  atualizarHorarioFuncionamento,
  obterHorariosFuncionamento,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { HorarioAtendimento } from 'components/HorarioAtendimento';
import { ListaHorarios } from 'components/HorarioAtendimento/hooks';

import { IconeEtapaHorarioAtendimento } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

export const HorarioAtendimentoEtapa = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const [listaHorarios, setListaHorarios] = useState<ListaHorarios[]>([]);

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const handleAvancar = async () => {
    setIsLoading(true);
    if (listaHorarios?.length === 0) {
      toast.warning(
        'É necessário informar ao menos um horário de funcionamento'
      );
      setIsLoading(false);
      return;
    }
    const atualizado = await atualizarHorarioFuncionamento(listaHorarios);
    if (atualizado) setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.TABELA_PRECO);
    setIsLoading(false);
  };

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.TEMPO_PREPARO);
  };

  const obterHorariosSalvos = useCallback(async () => {
    setIsLoading(true);
    const horariosSalvos = await obterHorariosFuncionamento();
    if (horariosSalvos) {
      setListaHorarios(horariosSalvos);
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    obterHorariosSalvos();
  }, [obterHorariosSalvos]);

  return (
    <>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Horário de Atendimento"
          handleVoltar={handleVoltar}
          icon={IconeEtapaHorarioAtendimento}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Informe aos usuários qual é o expediente da sua loja. Será
              possível editar os dias e horas a qualquer momento no painel de
              configurações do aplicativo.
            </Text>
          </VStack>
        </Box>
        <Box w="full" maxW="600px">
          <HorarioAtendimento
            listaHorarios={listaHorarios}
            setListaHorarios={setListaHorarios}
          />
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
        isDisabledAvancar={listaHorarios?.length === 0}
      />
    </>
  );
};
