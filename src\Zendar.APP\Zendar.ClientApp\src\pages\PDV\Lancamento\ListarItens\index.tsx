import {
  Box,
  Flex,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  VStack,
  Text,
  HStack,
  useMediaQuery,
  Tag,
} from '@chakra-ui/react';
import React, { Fragment, useEffect, useRef } from 'react';
import { useFormContext } from 'react-hook-form';

import { OperacaoItemObter } from 'helpers/api/Operacao/obterOperacaoComItens';
import { DecimalMask, moneyMask } from 'helpers/format/fieldsMasks';
import useWindowSize from 'helpers/layout/useWindowSize';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import ShadowScrollbar, {
  ShadowScrollbarForwardRefProps,
} from 'components/PDV/Geral/ShadowScrollbar';

import MenuItemOpcoes from './MenuItemOpcoes';
import Totalizadores from './Totalizadores';

const ListarItens = () => {
  const { watch } = useFormContext();
  const operacaoItens = watch('operacaoItens') as OperacaoItemObter[];
  const shadowScrollbarRef = useRef<ShadowScrollbarForwardRefProps>(null);
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { casasDecimais } = usePadronizacaoContext();
  const { height: windowHeight } = useWindowSize();

  useEffect(() => {
    setTimeout(() => {
      if (shadowScrollbarRef.current)
        shadowScrollbarRef.current.handleUpdateScrollbar();
    }, 200);
  }, [operacaoItens]);

  const TableDados = () => (
    <Table>
      <Tbody>
        {operacaoItens.map((operacaoItem: OperacaoItemObter) => (
          <Fragment key={operacaoItem.id}>
            <Tr
              sx={
                operacaoItem.informacoesComplementares
                  ? {
                      '& > td': { border: 'none', pb: '0' },
                    }
                  : {}
              }
            >
              <Td w={isLargerThan900 ? '55%' : '80%'} pl={0} pr={2} py={1}>
                <VStack alignItems="flex-start" spacing={0} pl="10px">
                  <Flex m={0}>
                    <Text
                      textColor="gray.700"
                      fontWeight="semibold"
                      fontSize="14px"
                      noOfLines={2}
                    >
                      {operacaoItem.produto}
                    </Text>
                  </Flex>
                  <HStack>
                    {operacaoItem.cor && (
                      <Tag
                        colorScheme="teal"
                        bg="teal.500"
                        color="white"
                        borderRadius="10px"
                        minW="40px"
                        padding="0px 12px"
                        justifyContent="center"
                        size="sm"
                      >
                        {operacaoItem.cor}
                      </Tag>
                    )}

                    {operacaoItem.tamanho && (
                      <Tag
                        colorScheme="pink"
                        bg="pink.500"
                        minW="40px"
                        justifyContent="center"
                        color="white"
                        borderRadius="10px"
                        size="sm"
                      >
                        {operacaoItem.tamanho}
                      </Tag>
                    )}
                    {!!operacaoItem.precoPromocional && !isLargerThan900 && (
                      <Flex
                        minW="64px"
                        textAlign="center"
                        px="0px"
                        justifyContent="center"
                        fontSize="10px"
                        fontWeight="800"
                        lineHeight="1"
                        align="center"
                        height="16px"
                        bg="yellow.600"
                        borderRadius="8px"
                        color="black"
                      >
                        PROMO
                      </Flex>
                    )}
                  </HStack>
                </VStack>
              </Td>
              {isLargerThan900 && (
                <>
                  <Td
                    w="10%"
                    fontWeight="semibold"
                    color="gray.700"
                    pl={0}
                    pr={2}
                    py={1}
                    isNumeric
                    fontSize="14px"
                  >
                    {DecimalMask(
                      operacaoItem.quantidade,
                      casasDecimais.casasDecimaisQuantidade
                    )}
                  </Td>
                  <Td w="10%" pl={0} pr={2} py={1} isNumeric>
                    <VStack spacing={0} alignItems="flex-end">
                      <Text
                        fontWeight="semibold"
                        color="gray.700"
                        fontSize="14px"
                      >
                        {DecimalMask(
                          operacaoItem.valorUnitario,
                          casasDecimais.casasDecimaisValor
                        )}
                      </Text>
                      {!!operacaoItem.precoPromocional && (
                        <Text
                          textDecoration="line-through"
                          color="gray.400"
                          fontWeight="700"
                          fontSize="14px"
                        >
                          {moneyMask(operacaoItem.valorVendaOriginal, false)}
                        </Text>
                      )}
                    </VStack>
                  </Td>
                  <Td w="10%" pl={0} pr={2} py={1} isNumeric>
                    <VStack spacing={0} alignItems="flex-end" gap="4px">
                      <Text
                        fontWeight="semibold"
                        color="red.500"
                        fontSize="14px"
                      >
                        {operacaoItem.valorDescontoItem > 0 ? '-' : null}
                        {DecimalMask(operacaoItem.valorDescontoItem, 2, 2)}
                      </Text>
                      {operacaoItem.valorDescontoItem > 0 &&
                        !operacaoItem.precoPromocional && (
                          <Flex
                            minW="64px"
                            textAlign="center"
                            px="0px"
                            justifyContent="center"
                            fontSize="10px"
                            fontWeight="400"
                            letterSpacing="0px"
                            lineHeight="1"
                            align="center"
                            height="16px"
                            bg="red.500"
                            borderRadius="8px"
                            color="white"
                          >
                            DESCONTO
                          </Flex>
                        )}
                      {!!operacaoItem.precoPromocional && (
                        <Flex
                          minW="64px"
                          textAlign="center"
                          px="0px"
                          justifyContent="center"
                          fontSize="10px"
                          fontWeight="800"
                          lineHeight="1"
                          align="center"
                          height="16px"
                          bg="yellow.600"
                          borderRadius="8px"
                          color="black"
                        >
                          PROMO
                        </Flex>
                      )}
                    </VStack>
                  </Td>
                </>
              )}
              <Td w="10%" pl={0} pr={isLargerThan900 ? 2 : 0} isNumeric>
                <Text fontWeight="semibold" color="blue.500" fontSize="14px">
                  {moneyMask(operacaoItem.valorItemComDesconto, false)}
                </Text>
                {!!operacaoItem.precoPromocional && !isLargerThan900 && (
                  <Text
                    textDecoration="line-through"
                    color="gray.400"
                    fontWeight="700"
                    fontSize="14px"
                  >
                    {moneyMask(operacaoItem.valorVendaOriginal, false)}
                  </Text>
                )}
              </Td>
              <Td w="5%" minW="40px" textAlign="right" color="gray.900" p={0}>
                <MenuItemOpcoes operacaoItem={operacaoItem} />
              </Td>
            </Tr>

            {operacaoItem.informacoesComplementares && (
              <Tr>
                <Td colSpan={6} p="0">
                  <Text noOfLines={1}>
                    {operacaoItem.informacoesComplementares}
                  </Text>
                </Td>
              </Tr>
            )}
          </Fragment>
        ))}
      </Tbody>
    </Table>
  );

  return operacaoItens && operacaoItens.length > 0 ? (
    <Box
      bg="gray.50"
      pr={{ base: 6, md: 12 }}
      pl={6}
      pt={isLargerThan900 ? 6 : 0}
      w="full"
      height="fit-content"
    >
      <Box
        bg="white"
        w="100%"
        height="fit-content"
        borderRadius="md"
        pl={5}
        py={1}
        boxShadow="base"
      >
        <Box pr={3}>
          <Table>
            <Thead>
              <Tr _hover={{}}>
                <Th
                  w={isLargerThan900 ? '55%' : '80%'}
                  pl="10px"
                  pr={2}
                  pb={1}
                  fontSize="12px"
                  color="gray.300"
                  fontWeight="500"
                >
                  Descrição
                </Th>
                {isLargerThan900 && (
                  <>
                    <Th w="10%" pl={0} pr={2} pb={1} isNumeric>
                      <Text fontSize="12px" color="gray.300" fontWeight="500">
                        Qtde.
                      </Text>
                    </Th>
                    <Th w="10%" pl={0} pr={2} pb={1} isNumeric>
                      <Text
                        fontSize="12px"
                        color="gray.300"
                        fontWeight="500"
                        whiteSpace="nowrap"
                      >
                        Valor un.
                      </Text>
                    </Th>
                    <Th w="10%" pl={0} pr={2} pb={1} isNumeric>
                      <Text
                        fontSize="12px"
                        color="gray.300"
                        fontWeight="500"
                        whiteSpace="nowrap"
                      >
                        Desconto
                      </Text>
                    </Th>
                  </>
                )}
                <Th
                  w="10%"
                  pl={0}
                  pr={isLargerThan900 ? 2 : 0}
                  pb={1}
                  isNumeric
                >
                  <Text
                    fontSize="12px"
                    color="gray.300"
                    whiteSpace="nowrap"
                    fontWeight="500"
                    pr={isLargerThan900 ? 'unset' : '32px'}
                  >
                    Valor total
                  </Text>
                </Th>
                {isLargerThan900 && <Th w="5%" p={0} />}
              </Tr>
            </Thead>
          </Table>
        </Box>

        <ShadowScrollbar
          width="100%"
          maxHeight={windowHeight - 380}
          paddingTop="0"
          shadowTopStyle={{
            background:
              'transparent linear-gradient(180deg, var(--white)  0%,  #FFFFFF00 100%) 0% 0% no-repeat padding-box',
            height: 30,
          }}
          shadowBottomStyle={{
            background:
              'transparent linear-gradient(180deg, #FFFFFF00 0%, var(--white) 100%) 0% 0% no-repeat padding-box',
            height: 30,
          }}
          ref={shadowScrollbarRef}
        >
          {operacaoItens && (
            <Box pr={isLargerThan900 ? 3 : 0}>
              <TableDados />
            </Box>
          )}
        </ShadowScrollbar>

        <Box pr={5}>
          <Totalizadores />
        </Box>
      </Box>
    </Box>
  ) : (
    <></>
  );
};

export default ListarItens;
