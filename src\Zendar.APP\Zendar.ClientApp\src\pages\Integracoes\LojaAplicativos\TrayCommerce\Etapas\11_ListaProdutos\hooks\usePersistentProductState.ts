import { useCallback, useState } from 'react';

import { ProdutoSemCadastroTrayProps } from 'api/Integrações/Tray';

import { useProdutosStore } from '../../produtos';
import { ProdutoSelecionado } from '../../types';

type PersistentProductState = {
  produtosSelecionados: ProdutoSelecionado[];
  produtosSalvos: ProdutoSelecionado[];
  adicionarProdutoSelecionado: (produto: ProdutoSemCadastroTrayProps) => void;
  removerProdutoSelecionado: (produtoId: string) => void;
  limparProdutosSelecionados: () => void;
  alternarSelecaoProduto: (produto: ProdutoSemCadastroTrayProps) => void;
  atualizarVariacoesProduto: (produtoId: string, variacoes: string[]) => void;
  salvarProdutosSelecionados: () => void;
  removeProdutoSalvo: (produtoId: string) => void;
  limpaProdutosSalvos: () => void;
  checaProdutoEstaSelecionado: (produtoId: string) => boolean;
  checaProdutoEstaSalvo: (produtoId: string) => boolean;
  deveIncluirProdutoNaListagem: (
    produto: ProdutoSemCadastroTrayProps,
    listaAtual: ProdutoSemCadastroTrayProps[]
  ) => boolean;

  quantidadeProdutosSelecionados: number;
  quantidadeProdutosSalvos: number;
  todosProdutosEstaoSelecionados: (
    listaProdutos: ProdutoSemCadastroTrayProps[]
  ) => boolean;
};

export const usePersistentProductState = (): PersistentProductState => {
  const [produtosSelecionados, setProdutosSelecionados] = useState<
    ProdutoSelecionado[]
  >([]);

  const produtosSalvos = useProdutosStore((state) => state.produtos);
  const adicionarVariosNoStore = useProdutosStore(
    (state) => state.adicionarVarios
  );
  const removerDoStore = useProdutosStore((state) => state.remover);
  const limparStore = useProdutosStore((state) => state.limpar);

  const converterParaProdutoSelecionado = useCallback(
    (produto: ProdutoSemCadastroTrayProps): ProdutoSelecionado => {
      const pegarIdsVariacoes = (
        item: ProdutoSemCadastroTrayProps
      ): string[] => {
        if (item.tipoProduto !== 2) return [];
        return (item.produtoCores ?? [])
          .flatMap((cor) => cor.produtoCorTamanhos ?? [])
          .map((ct) => ct.produtoCorTamanho.produtoCorTamanhoId);
      };

      return {
        produtoId: produto.id,
        descricao: produto.nome,
        listaProdutoCorTamanhoId: pegarIdsVariacoes(produto),
      };
    },
    []
  );

  const adicionarProdutoSelecionado = useCallback(
    (produto: ProdutoSemCadastroTrayProps) => {
      const produtoSelecionado = converterParaProdutoSelecionado(produto);

      setProdutosSelecionados((prev) => {
        const jaExiste = prev.some((p) => p.produtoId === produto.id);
        if (jaExiste) return prev;

        return [...prev, produtoSelecionado];
      });
    },
    [converterParaProdutoSelecionado]
  );

  const removerProdutoSelecionado = useCallback((produtoId: string) => {
    setProdutosSelecionados((prev) =>
      prev.filter((p) => p.produtoId !== produtoId)
    );
  }, []);

  const limparProdutosSelecionados = useCallback(() => {
    setProdutosSelecionados([]);
  }, []);

  const alternarSelecaoProduto = useCallback(
    (produto: ProdutoSemCadastroTrayProps) => {
      const jaEstaSelecionado = produtosSelecionados.some(
        (p) => p.produtoId === produto.id
      );

      if (jaEstaSelecionado) {
        removerProdutoSelecionado(produto.id);
      } else {
        adicionarProdutoSelecionado(produto);
      }
    },
    [
      produtosSelecionados,
      removerProdutoSelecionado,
      adicionarProdutoSelecionado,
    ]
  );

  const atualizarVariacoesProduto = useCallback(
    (produtoId: string, variacoes: string[]) => {
      setProdutosSelecionados((prev) =>
        prev.map((produto) =>
          produto.produtoId === produtoId
            ? { ...produto, listaProdutoCorTamanhoId: variacoes }
            : produto
        )
      );
    },
    []
  );

  const salvarProdutosSelecionados = useCallback(() => {
    if (produtosSelecionados.length > 0) {
      adicionarVariosNoStore(produtosSelecionados, produtosSalvos);
      limparProdutosSelecionados();
    }
  }, [
    produtosSelecionados,
    produtosSalvos,
    adicionarVariosNoStore,
    limparProdutosSelecionados,
  ]);

  const removeProdutoSalvo = useCallback(
    (produtoId: string) => {
      removerDoStore(produtoId);
    },
    [removerDoStore]
  );

  const limpaProdutosSalvos = useCallback(() => {
    limparStore();
  }, [limparStore]);

  const checaProdutoEstaSelecionado = useCallback(
    (produtoId: string): boolean => {
      return produtosSelecionados.some((p) => p.produtoId === produtoId);
    },
    [produtosSelecionados]
  );

  const checaProdutoEstaSalvo = useCallback(
    (produtoId: string): boolean => {
      return produtosSalvos.some((p) => p.produtoId === produtoId);
    },
    [produtosSalvos]
  );

  const deveIncluirProdutoNaListagem = useCallback(
    (
      produto: ProdutoSemCadastroTrayProps,
      listaAtual: ProdutoSemCadastroTrayProps[]
    ): boolean => {
      const duplicado = listaAtual.some((p) => p.id === produto.id);
      const jaSalvo = checaProdutoEstaSalvo(produto.id);

      return !duplicado && !jaSalvo;
    },
    [checaProdutoEstaSalvo]
  );

  const todosProdutosEstaoSelecionados = useCallback(
    (listaProdutos: ProdutoSemCadastroTrayProps[]): boolean => {
      if (listaProdutos.length === 0) return false;

      return listaProdutos.every((produto) =>
        checaProdutoEstaSelecionado(produto.id)
      );
    },
    [checaProdutoEstaSelecionado]
  );

  return {
    produtosSelecionados,
    produtosSalvos,
    adicionarProdutoSelecionado,
    removerProdutoSelecionado,
    limparProdutosSelecionados,
    alternarSelecaoProduto,
    atualizarVariacoesProduto,
    salvarProdutosSelecionados,
    removeProdutoSalvo,
    limpaProdutosSalvos,
    checaProdutoEstaSelecionado,
    checaProdutoEstaSalvo,
    deveIncluirProdutoNaListagem,
    quantidadeProdutosSelecionados: produtosSelecionados.length,
    quantidadeProdutosSalvos: produtosSalvos.length,
    todosProdutosEstaoSelecionados,
  };
};
