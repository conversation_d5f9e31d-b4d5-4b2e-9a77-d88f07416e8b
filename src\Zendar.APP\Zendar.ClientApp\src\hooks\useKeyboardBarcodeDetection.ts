import { useCallback, useRef, useEffect } from 'react';

interface KeyboardBarcodeDetectionOptions {
  minLength?: number;
  maxTimeBetweenKeys?: number;
  endKeys?: string[];
  enabled?: boolean;
}

interface KeyboardBarcodeDetectionResult {
  isFromScanner: boolean;
  scannedValue: string;
}

export const useKeyboardBarcodeDetection = (
  onBarcodeScanned: (result: KeyboardBarcodeDetectionResult) => void,
  options: KeyboardBarcodeDetectionOptions = {}
) => {
  const {
    minLength = 8,
    maxTimeBetweenKeys = 100,
    endKeys = ['Enter', 'Tab'],
    enabled = true,
  } = options;

  const keyBuffer = useRef<string>('');
  const keyTimes = useRef<number[]>([]);
  const lastKeyTime = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const resetBuffer = useCallback(() => {
    keyBuffer.current = '';
    keyTimes.current = [];
    lastKeyTime.current = 0;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const processBuffer = useCallback(() => {
    const buffer = keyBuffer.current;
    const times = keyTimes.current;

    if (buffer.length >= minLength && times.length >= minLength) {
      const totalTime = times[times.length - 1] - times[0];
      const avgTimeBetweenKeys = totalTime / (times.length - 1);

      const isFromScanner = avgTimeBetweenKeys <= maxTimeBetweenKeys;

      onBarcodeScanned({
        isFromScanner,
        scannedValue: buffer,
      });
    }

    resetBuffer();
  }, [minLength, maxTimeBetweenKeys, onBarcodeScanned, resetBuffer]);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      const now = Date.now();
      const key = event.key;

      if (endKeys.includes(key)) {
        if (keyBuffer.current.length > 0) {
          processBuffer();
        }
        return;
      }

      if (key.length > 1 && !['Backspace', 'Delete'].includes(key)) {
        return;
      }

      if (
        lastKeyTime.current > 0 &&
        now - lastKeyTime.current > maxTimeBetweenKeys * 3
      ) {
        resetBuffer();
      }

      if (key === 'Backspace' || key === 'Delete') {
        keyBuffer.current = keyBuffer.current.slice(0, -1);
        keyTimes.current.pop();
      } else if (key.length === 1) {
        keyBuffer.current += key;
        keyTimes.current.push(now);
      }

      lastKeyTime.current = now;

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        if (keyBuffer.current.length >= minLength) {
          processBuffer();
        } else {
          resetBuffer();
        }
      }, maxTimeBetweenKeys * 2);
    },
    [
      enabled,
      endKeys,
      maxTimeBetweenKeys,
      minLength,
      processBuffer,
      resetBuffer,
    ]
  );

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }
  }, [enabled, handleKeyDown]);

  return {
    resetBuffer,
  };
};
