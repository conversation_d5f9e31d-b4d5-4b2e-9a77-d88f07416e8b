import { useCallback, useState } from 'react';
import { BiLink } from 'react-icons/bi';
import { IoMdAddCircleOutline } from 'react-icons/io';
import { IconType } from 'react-icons/lib';
import { toast } from 'react-toastify';

import { checkIsGuidEmpty } from 'helpers/validation/checkIsGuidEmpty';

import api, { ResponseApi } from 'services/api';
import { abrirModalConfirmacaoImportarPedidoNovamente } from 'services/tray';

import { VendaOuPedidoPendenteProps } from 'pages/Integracoes/LojaAplicativos/TrayCommerce/PainelAdministradorTray/VendasTray/validationForms';

import { ModalEscolherClienteCpfOuCnpjDuplicado } from 'components/v2/Integracoes/Tray/Modal/ModalEscolherClienteCpfOuCnpjDuplicado';
import { ModalInformarOrigemVendaIntermediador } from 'components/v2/Integracoes/Tray/Modal/ModalInformarOrigemVendaIntermediador';
import { ModalPreencherNcmProduto } from 'components/v2/Integracoes/Tray/Modal/ModalPreencherNcmProduto';
import { ModalVincularFormaRecebimento } from 'components/v2/Integracoes/Tray/Modal/ModalVincularFormaRecebimento';
import { ModalVincularProduto } from 'components/v2/Integracoes/Tray/Modal/ModalVincularProduto';
import { ModalVincularSituacao } from 'components/v2/Integracoes/Tray/Modal/ModalVincularSituacao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { enumIdentificacaoIntegracao } from 'constants/enum/enumIdentificacaoIntegracao';
import { TipoCadastro, TipoPendencia } from 'constants/enum/enumTrayPendencias';

import { ModalVincularVariacao } from '../ModalVincularVariacao';

export const useModalPendencia = (
  listaVendasOuPedidosPendentes: VendaOuPedidoPendenteProps[],
  setListaVendasOuPedidosPendentes: React.Dispatch<
    React.SetStateAction<VendaOuPedidoPendenteProps[]>
  >,
  onClose: () => void,
  isOpen: boolean,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  reprocessarPedidoReloadListagem: () => Promise<void>,
  pedidoId: string
) => {
  const [reprocessandoPendencias, setReprocessandoPendencias] = useState(false);
  const [
    animacaoCorrecaoPendenciasIniciada,
    setAnimacaoCorrecaoPendenciasIniciada,
  ] = useState(false);
  const [houvePendenciasResolvidas, setHouvePendenciasResolvidas] =
    useState(false);

  const ultimaPendencia =
    listaVendasOuPedidosPendentes?.filter(
      (item) => item.clienteFornecedorId === null
    )?.length === 1;

  const iniciarAnimacaoReprocessandoPendencia = async () => {
    setAnimacaoCorrecaoPendenciasIniciada(true);

    setTimeout(() => {
      setReprocessandoPendencias(true);
    }, 500);

    setTimeout(() => {
      setAnimacaoCorrecaoPendenciasIniciada(false);
    }, 1000);
  };

  const confirmarCorrecoesPendencia = useCallback(async () => {
    if (ultimaPendencia) {
      setIsLoading(true);
      await reprocessarPedidoReloadListagem();
      setIsLoading(false);
    } else {
      iniciarAnimacaoReprocessandoPendencia();
    }
  }, [ultimaPendencia, setIsLoading, reprocessarPedidoReloadListagem]);

  const marcarPendenciaComoResolvida = useCallback(
    (pendenciaId: string) => {
      setIsLoading(true);

      setListaVendasOuPedidosPendentes((prev) =>
        prev?.map((itemPendente) => ({
          ...itemPendente,
          possuiPendencia:
            itemPendente.id === pendenciaId
              ? false
              : itemPendente.possuiPendencia,
        }))
      );
      setHouvePendenciasResolvidas(true);

      setIsLoading(false);
    },
    [setIsLoading, setListaVendasOuPedidosPendentes]
  );

  const importarProduto = useCallback(
    async (
      cadastroPlataformaId: string,
      pendenciaId: string,
      descricaoProduto: string
    ) => {
      setIsLoading(true);
      const routeApi =
        ConstanteEnderecoWebservice.INTEGRACAO_IMPORTAR_PRODUTO.replace(
          '{identificacaoIntegracao}',
          enumIdentificacaoIntegracao.TRAY.toString()
        )
          .replace('{pedidoId}', pedidoId)
          .replace('{pendenciaId}', pendenciaId)
          .replace('{produtoPlataformaId}', cadastroPlataformaId);

      const response = await api.post<void, ResponseApi<string>>(routeApi);

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((avisos) => toast.warning(avisos));
        }
        if (response.sucesso) {
          if (checkIsGuidEmpty(response.dados)) {
            toast.error('Não foi possível cadastrar o produto');
            setIsLoading(false);
            return;
          }
          ModalPreencherNcmProduto({
            produtoId: response.dados,
            resolverPendencia: () => {
              marcarPendenciaComoResolvida(pendenciaId);
            },
            descricaoProduto,
            pendenciaId,
            pedidoId,
            realizandoCadastroProduto: true,
          });
        }
        setIsLoading(false);
      }
      setIsLoading(false);
    },

    [marcarPendenciaComoResolvida, pedidoId, setIsLoading]
  );

  const vincularClienteCpfOuCnpjDuplicado = useCallback(
    async ({
      siteId,
      zendarId,
      pendenciaId,
    }: {
      siteId: string;
      zendarId: string;
      pendenciaId: string;
    }) => {
      setIsLoading(true);
      const routeApi =
        ConstanteEnderecoWebservice.INTEGRACAO_VINCULAR_CLIENTE.replace(
          '{identificacaoIntegracao}',
          enumIdentificacaoIntegracao.TRAY.toString()
        )
          .replace('{pedidoId}', pedidoId)
          .replace('{pendenciaId}', pendenciaId);
      const response = await api.post<void, ResponseApi>(routeApi, {
        zendarId,
        siteId,
      });

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
          setIsLoading(false);

          return false;
        }
        if (response.sucesso) {
          toast.success('Cliente vinculado com sucesso');
          setIsLoading(false);
          return true;
        }
      }
      setIsLoading(false);
      return false;
    },
    [pedidoId, setIsLoading]
  );

  const handleVincularClienteCpfOuCnpjDuplicado = useCallback(
    async ({
      pendenciaId,
      cpfCnpjDuplicado,
    }: {
      pendenciaId: string;
      cpfCnpjDuplicado: string;
    }) => {
      await ModalEscolherClienteCpfOuCnpjDuplicado({
        pedidoId,
        cpfCnpjDuplicado,
        resolverPendencia: () => {
          marcarPendenciaComoResolvida(pendenciaId);
        },
        vincularClienteCPFDuplicado: async (idClienteEscolhido: string) => {
          return vincularClienteCpfOuCnpjDuplicado({
            siteId: cpfCnpjDuplicado,
            zendarId: idClienteEscolhido,
            pendenciaId,
          });
        },
      });
    },
    [marcarPendenciaComoResolvida, pedidoId, vincularClienteCpfOuCnpjDuplicado]
  );

  const ativarProduto = async ({
    pendenciaId,
    produtoId,
  }: {
    pendenciaId: string;
    produtoId: string;
  }) => {
    setIsLoading(true);
    const routeApi =
      ConstanteEnderecoWebservice.INTEGRACAO_ATIVAR_PRODUTO.replace(
        '{identificacaoIntegracao}',
        enumIdentificacaoIntegracao.TRAY.toString()
      )
        .replace('{pedidoId}', pedidoId)
        .replace('{pendenciaId}', pendenciaId)
        .replace('{produtoId}', produtoId);
    const response = await api.post<void, ResponseApi>(routeApi);
    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Produto ativado com sucesso');
        marcarPendenciaComoResolvida(pendenciaId);
      }
    }
    setIsLoading(false);
  };

  function extrairAtributos(texto: string) {
    let corSemVincular = '';
    let tamanhoSemVincular = '';

    const regexCor = /cor:\s*([^|]+)/i;
    const matchCor = texto.match(regexCor);
    if (matchCor) {
      corSemVincular = matchCor[1].trim();
    }

    const regexTamanho = /tamanho:\s*([^|]+)/i;
    const matchTamanho = texto.match(regexTamanho);
    if (matchTamanho) {
      tamanhoSemVincular = matchTamanho[1].trim();
    }

    return { corSemVincular, tamanhoSemVincular };
  }

  const acaoImportaPedidoNovamente = {
    content: 'Importar pedido novamente',
    onClick: async () => {
      await abrirModalConfirmacaoImportarPedidoNovamente({
        pedidoId,
        callback: async () => {
          setTimeout(async () => {
            window.location.reload();
          }, 200);
        },
      });
    },
  };

  const criarChave = (tipoCadastro: number, tipoPendencia: number) =>
    `${tipoCadastro}.${tipoPendencia}`;

  const acoesParaPendencias = (
    pendences: VendaOuPedidoPendenteProps,
    newIcon: (icon?: IconType) => JSX.Element,
    trayId: string
  ) => {
    const {
      tipoPendencia,
      tipoCadastro,
      descricao,
      cadastroPlataformaId,
      id: pendenciaId,
      descricaoProduto,
      cadastroSistemaId,
      tipoProduto,
    } = pendences;
    const pendencia = criarChave(tipoCadastro, tipoPendencia);
    const acoesDaPendencia = {
      [criarChave(TipoCadastro.MARKETPLACE, TipoPendencia.CADASTRO_PLATAFORMA)]:
        [
          {
            icon: newIcon(BiLink),
            content: 'Informar a origem da venda(Intermediador)',
            onClick: () => {
              ModalInformarOrigemVendaIntermediador({
                pendenciaId,
                resolverPendencia: () => {
                  marcarPendenciaComoResolvida(pendenciaId);
                },
                siteId: cadastroPlataformaId,
                pedidoId,
                vinculandoNovoIntermediador: true,
                trayId,
              });
            },
          },
        ],
      [criarChave(TipoCadastro.SITUACAO, TipoPendencia.VINCULO)]: [
        {
          icon: newIcon(BiLink),
          content: 'Vincule a situação',
          onClick: () => {
            ModalVincularSituacao({
              pendenciaId,
              pedidoId,
              descricaoSituacao: descricao,
              resolverPendencia: () => {
                marcarPendenciaComoResolvida(pendenciaId);
              },
              siteId: cadastroPlataformaId,
              trayId,
            });
          },
        },
      ],
      [criarChave(TipoCadastro.PRODUTO, TipoPendencia.CADASTRO_INATIVO)]: [
        {
          content: 'Ativar o produto',
          onClick: async () => {
            ativarProduto({
              pendenciaId,
              produtoId: cadastroSistemaId,
            });
          },
        },
      ],
      [criarChave(TipoCadastro.CLIENTE, TipoPendencia.CADASTRO_VINCULO)]: [
        {
          icon: newIcon(BiLink),
          content: 'Selecionar cliente para vincular',
          onClick: () =>
            handleVincularClienteCpfOuCnpjDuplicado({
              pendenciaId,
              cpfCnpjDuplicado: cadastroPlataformaId,
            }),
        },
      ],
      [criarChave(TipoCadastro.CLIENTE, TipoPendencia.SUPORTE)]: [
        acaoImportaPedidoNovamente,
      ],
      [criarChave(TipoCadastro.VARIACAO, TipoPendencia.SUPORTE)]: [
        acaoImportaPedidoNovamente,
      ],
      [criarChave(TipoCadastro.PRODUTO, TipoPendencia.CADASTRO_VINCULO)]: [
        {
          icon: newIcon(IoMdAddCircleOutline),
          content: 'Importar produto',
          onClick: () =>
            importarProduto(
              cadastroPlataformaId,
              pendenciaId,
              descricaoProduto
            ),
        },

        {
          icon: newIcon(BiLink),
          content: 'Vincular a cadastro existente',
          onClick: () => {
            ModalVincularProduto({
              nameProduto: descricao,
              cadastroPlataformaId,
              resolverPendencia: () => {
                marcarPendenciaComoResolvida(pendenciaId);
              },
              tipoProduto,
              pedidoId,
              pendenciaId,
            });
          },
        },
      ],

      [criarChave(TipoCadastro.PRODUTO, TipoPendencia.NCM)]: [
        {
          icon: newIcon(BiLink),
          content: 'Preencher NCM do produto',
          onClick: () => {
            ModalPreencherNcmProduto({
              produtoId: cadastroSistemaId,
              descricaoProduto,
              resolverPendencia: () => {
                marcarPendenciaComoResolvida(pendenciaId);
              },
              pedidoId,
              pendenciaId,
            });
          },
        },
      ],
      [criarChave(TipoCadastro.PRODUTO, TipoPendencia.SUPORTE)]: [
        acaoImportaPedidoNovamente,
      ],
      [criarChave(TipoCadastro.FORMA_RECEBIMENTO, TipoPendencia.VINCULO)]: [
        {
          icon: newIcon(BiLink),
          content: 'Vincular a forma recebimento',
          onClick: () => {
            ModalVincularFormaRecebimento({
              pendenciaId,
              pedidoId,
              nomeFormaRecebimentoParaVincular: descricao,
              resolverPendencia: () => {
                marcarPendenciaComoResolvida(pendenciaId);
              },
              siteId: cadastroPlataformaId,
            });
          },
        },
      ],
      [criarChave(TipoCadastro.VARIACAO, TipoPendencia.CADASTRO_VINCULO)]: [
        {
          icon: newIcon(BiLink),
          content: 'Vincular a variação',
          onClick: () => {
            ModalVincularVariacao({
              produtoId: cadastroSistemaId,
              descricaoProduto,
              corSemVincular:
                extrairAtributos(descricaoProduto)?.corSemVincular,
              tamanhoSemVincular:
                extrairAtributos(descricaoProduto)?.tamanhoSemVincular,
              resolverPendencia: () => {
                marcarPendenciaComoResolvida(pendenciaId);
              },
              pedidoId,
              pendenciaId,
              siteId: cadastroPlataformaId,
            });
          },
        },
      ],
      [criarChave(TipoCadastro.PAGAMENTO, TipoPendencia.SUPORTE)]: [
        acaoImportaPedidoNovamente,
      ],
    };

    return acoesDaPendencia[pendencia];
  };

  return {
    onClose,
    acoesParaPendencias,
    confirmarCorrecoesPendencia,
    houvePendenciasResolvidas,
    isOpen,
    listaVendasOuPedidosPendentes,
    reprocessandoPendencias,
    animacaoCorrecaoPendenciasIniciada,
  };
};
