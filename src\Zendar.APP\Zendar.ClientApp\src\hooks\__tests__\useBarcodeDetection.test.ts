import { renderHook, act } from '@testing-library/react';
import { useBarcodeDetection } from '../useBarcodeDetection';

// Mock Date.now para controlar o tempo nos testes
const mockDateNow = jest.fn();
Date.now = mockDateNow;

describe('useBarcodeDetection', () => {
  beforeEach(() => {
    mockDateNow.mockClear();
    mockDateNow.mockReturnValue(1000); // Tempo inicial
  });

  it('deve detectar entrada instantânea como leitor', () => {
    const { result } = renderHook(() => useBarcodeDetection({
      minLength: 8,
      instantInputThreshold: 100
    }));

    mockDateNow.mockReturnValue(1000);
    
    act(() => {
      const detection = result.current.detectBarcodeInput('12345678901234');
      expect(detection.isFromScanner).toBe(true);
    });
  });

  it('deve detectar múltiplos caracteres de uma vez como leitor', () => {
    const { result } = renderHook(() => useBarcodeDetection({
      minLength: 8
    }));

    mockDateNow.mockReturnValue(1000);
    
    // Simula entrada vazia primeiro
    act(() => {
      result.current.detectBarcodeInput('');
    });

    mockDateNow.mockReturnValue(1010);
    
    // Simula entrada de muitos caracteres de uma vez
    act(() => {
      const detection = result.current.detectBarcodeInput('123456789012');
      expect(detection.isFromScanner).toBe(true);
    });
  });

  it('deve detectar digitação manual como não-leitor', () => {
    const { result } = renderHook(() => useBarcodeDetection({
      minLength: 8,
      maxTimeBetweenChars: 50
    }));

    let time = 1000;
    mockDateNow.mockImplementation(() => time);

    // Simula digitação caractere por caractere com intervalos longos
    const chars = '12345678';
    let currentValue = '';

    for (let i = 0; i < chars.length; i++) {
      currentValue += chars[i];
      time += 200; // 200ms entre cada caractere (muito lento para leitor)
      
      act(() => {
        const detection = result.current.detectBarcodeInput(currentValue);
        if (i === chars.length - 1) {
          expect(detection.isFromScanner).toBe(false);
        }
      });
    }
  });

  it('deve detectar digitação rápida como leitor', () => {
    const { result } = renderHook(() => useBarcodeDetection({
      minLength: 8,
      maxTimeBetweenChars: 50,
      minSpeed: 10
    }));

    let time = 1000;
    mockDateNow.mockImplementation(() => time);

    // Simula digitação caractere por caractere muito rápida
    const chars = '12345678';
    let currentValue = '';

    for (let i = 0; i < chars.length; i++) {
      currentValue += chars[i];
      time += 20; // 20ms entre cada caractere (muito rápido)
      
      act(() => {
        const detection = result.current.detectBarcodeInput(currentValue);
        if (i === chars.length - 1) {
          expect(detection.isFromScanner).toBe(true);
        }
      });
    }
  });

  it('deve retornar false para strings muito curtas', () => {
    const { result } = renderHook(() => useBarcodeDetection({
      minLength: 8
    }));

    mockDateNow.mockReturnValue(1000);
    
    act(() => {
      const detection = result.current.detectBarcodeInput('123');
      expect(detection.isFromScanner).toBe(false);
    });
  });

  it('deve resetar corretamente', () => {
    const { result } = renderHook(() => useBarcodeDetection());

    mockDateNow.mockReturnValue(1000);
    
    // Adiciona algum conteúdo
    act(() => {
      result.current.detectBarcodeInput('12345678');
    });

    // Reseta
    act(() => {
      result.current.resetDetection();
    });

    // Verifica se o próximo input é tratado como novo
    mockDateNow.mockReturnValue(2000);
    act(() => {
      const detection = result.current.detectBarcodeInput('87654321');
      // Deve funcionar normalmente após reset
      expect(detection.inputValue).toBe('87654321');
    });
  });

  it('deve lidar com input sendo limpo', () => {
    const { result } = renderHook(() => useBarcodeDetection());

    mockDateNow.mockReturnValue(1000);
    
    // Adiciona conteúdo
    act(() => {
      result.current.detectBarcodeInput('12345678');
    });

    // Limpa o input
    act(() => {
      const detection = result.current.detectBarcodeInput('');
      expect(detection.isFromScanner).toBe(false);
    });
  });
});
