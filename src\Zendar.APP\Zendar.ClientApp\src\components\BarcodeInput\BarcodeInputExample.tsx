import React, { useState, useCallback } from 'react';
import { Box, Input, Text, VStack, Badge } from '@chakra-ui/react';
import { useBarcodeDetection } from 'hooks/useBarcodeDetection';
import { useKeyboardBarcodeDetection } from 'hooks/useKeyboardBarcodeDetection';

interface BarcodeInputExampleProps {
  onBarcodeDetected?: (value: string, isFromScanner: boolean) => void;
  placeholder?: string;
  minLength?: number;
}

export const BarcodeInputExample: React.FC<BarcodeInputExampleProps> = ({
  onBarcodeDetected,
  placeholder = "Digite ou escaneie um código de barras",
  minLength = 8,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [detectionResult, setDetectionResult] = useState<{
    isFromScanner: boolean;
    method: 'input' | 'keyboard' | null;
  }>({ isFromScanner: false, method: null });

  // Hook para detecção baseada em mudanças no input
  const { detectBarcodeInput, resetDetection } = useBarcodeDetection({
    minLength,
    maxTimeBetweenChars: 50,
    minSpeed: 10,
    instantInputThreshold: 100,
  });

  // Hook para detecção baseada em eventos de teclado
  const handleKeyboardBarcode = useCallback(
    (result: { isFromScanner: boolean; scannedValue: string }) => {
      if (result.isFromScanner && result.scannedValue.length >= minLength) {
        setInputValue(result.scannedValue);
        setDetectionResult({ isFromScanner: true, method: 'keyboard' });
        onBarcodeDetected?.(result.scannedValue, true);
      }
    },
    [minLength, onBarcodeDetected]
  );

  useKeyboardBarcodeDetection(handleKeyboardBarcode, {
    minLength,
    maxTimeBetweenKeys: 100,
    endKeys: ['Enter', 'Tab'],
    enabled: true,
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputValue(value);

    // Detecta usando o hook de input
    const result = detectBarcodeInput(value);
    setDetectionResult({ 
      isFromScanner: result.isFromScanner, 
      method: result.isFromScanner ? 'input' : null 
    });

    if (result.isFromScanner) {
      onBarcodeDetected?.(value, true);
    }
  };

  const handleInputClear = () => {
    setInputValue('');
    setDetectionResult({ isFromScanner: false, method: null });
    resetDetection();
  };

  return (
    <VStack spacing={4} align="stretch">
      <Box>
        <Input
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          size="lg"
          borderColor={detectionResult.isFromScanner ? 'green.500' : 'gray.200'}
          borderWidth={detectionResult.isFromScanner ? '2px' : '1px'}
        />
      </Box>

      {inputValue && (
        <Box>
          <Text fontSize="sm" color="gray.600" mb={2}>
            Status da detecção:
          </Text>
          <Badge
            colorScheme={detectionResult.isFromScanner ? 'green' : 'gray'}
            size="lg"
            px={3}
            py={1}
          >
            {detectionResult.isFromScanner 
              ? `✓ Detectado como leitor (${detectionResult.method})` 
              : '✗ Digitação manual'
            }
          </Badge>
        </Box>
      )}

      {inputValue && (
        <Box>
          <Text fontSize="sm" color="gray.600" mb={1}>
            Valor atual:
          </Text>
          <Text fontSize="md" fontFamily="mono" bg="gray.50" p={2} borderRadius="md">
            {inputValue}
          </Text>
        </Box>
      )}

      <Box>
        <Text fontSize="xs" color="gray.500">
          💡 Dicas:
          <br />
          • Leitores geralmente inserem texto muito rapidamente
          <br />
          • Códigos de barras têm pelo menos {minLength} caracteres
          <br />
          • O sistema detecta tanto entrada por input quanto por teclado
        </Text>
      </Box>
    </VStack>
  );
};
