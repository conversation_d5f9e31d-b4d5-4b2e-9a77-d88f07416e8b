import {
  Text,
  VStack,
  Box,
  useMediaQuery,
  Flex,
  GridItem,
} from '@chakra-ui/react';
import { useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoIosArrowDown } from 'react-icons/io';

import { atualizarTempoPreparo, obterTempoPreparo } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import InputDateOrTime from 'components/v2/ui/InputDateOrTime';

import { IconeEtapaTempoPreparo } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { FormData, valoresPadrao, yupResolver } from './validationForms';

export const TempoPreparo = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: valoresPadrao,
  });

  const { handleSubmit } = formMethods;

  const handleAvancar = handleSubmit(async (dados) => {
    setIsLoading(true);
    const response = await atualizarTempoPreparo(dados);
    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.HORARIO_FUNCIONAMENTO);
    }

    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.COR_PERSONALIZADA);
  };

  const obterTempoPreparoSalvos = useCallback(async () => {
    setIsLoading(true);
    const horariosSalvos = await obterTempoPreparo();
    if (horariosSalvos) {
      formMethods.reset({
        minimo: horariosSalvos.minimo || valoresPadrao.minimo,
        maximo: horariosSalvos.maximo || valoresPadrao.maximo,
      });
    }
    setIsLoading(false);
  }, [formMethods, setIsLoading]);

  useEffect(() => {
    obterTempoPreparoSalvos();
  }, [obterTempoPreparoSalvos]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height="100vh">
        <Header
          title="Tempo de Preparo"
          handleVoltar={handleVoltar}
          icon={IconeEtapaTempoPreparo}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
              whiteSpace="pre-line"
            >
              Informe um intervalo de tempo médio (estimado) para que a loja
              receba o pedido do cliente e finalize o preparo. Essa informação
              permanecerá visível para todos os usuários.
              <Text as="span" fontStyle="italic">
                (Ex: “Tempo de preparo dos pedidos estimado entre 30 e 45
                minutos”)
              </Text>
              {' \n'}
              **Dica: O tempo de preparo tem grande relevância tanto para a
              decisão de compra do cliente quanto para a reputação do seu
              estabelecimento.
            </Text>
          </VStack>
          <VStack
            mb={['0', '22px', '22px']}
            color="primary.50"
            fontSize="14px"
            align="start"
            spacing="0px"
          >
            <Text letterSpacing="0px" color="black">
              Intervalo de tempo estimado para o preparo
            </Text>
            <Flex
              bg="gray.100"
              borderColor="gray.200"
              borderWidth="1px"
              borderStyle="solid"
              borderRadius="5px"
              height={['fit-content', '136px']}
              w="full"
              justify="center"
              py="32px"
            >
              <SimpleGridForm
                gap={{ base: 4, sm: 6 }}
                w="full"
                px={['24px', '108px']}
              >
                <GridItem colSpan={[12, 6, 6]}>
                  <InputDateOrTime
                    type="time"
                    name="minimo"
                    label="Tempo mínimo"
                    color="gray.700"
                    icon={IoIosArrowDown}
                  />
                </GridItem>
                <GridItem colSpan={[12, 6, 6]}>
                  <InputDateOrTime
                    type="time"
                    name="maximo"
                    label="Tempo máximo"
                    color="gray.700"
                    icon={IoIosArrowDown}
                  />
                </GridItem>
              </SimpleGridForm>
            </Flex>
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
