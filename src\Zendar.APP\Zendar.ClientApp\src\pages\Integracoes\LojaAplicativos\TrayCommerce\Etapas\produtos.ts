import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { ProdutoSelecionado } from './types';

export const LISTA_PRODUTOS_TRAY_STORAGE_KEY = 'listarProdutoStorage';

type ProdutosState = {
  produtos: ProdutoSelecionado[];
  adicionarVarios: (
    novos: ProdutoSelecionado[],
    salvos?: ProdutoSelecionado[]
  ) => void;
  remover: (id: string) => void;
  limpar: () => void;
};

export const useProdutosStore = create<ProdutosState>()(
  persist(
    (set, get) => ({
      produtos: [],
      adicionarVarios: (produtosSendoSalvos, produtosJaSalvos = []) => {
        const novos =
          produtosJaSalvos.length > 0
            ? [...produtosSendoSalvos, ...produtosJaSalvos]
            : produtosSendoSalvos;

        set({ produtos: [...novos] });
      },
      remover: (id) =>
        set({ produtos: get().produtos.filter((x) => x.produtoId !== id) }),
      limpar: () => set({ produtos: [] }),
    }),
    {
      name: LISTA_PRODUTOS_TRAY_STORAGE_KEY,
    }
  )
);
