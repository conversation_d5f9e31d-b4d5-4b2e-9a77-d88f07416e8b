import { Text, VStack, Box, useMediaQuery, GridItem } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { obterPerfilLojaDelivery } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';
import { usePerfilLoja } from 'store/Cardapio/perfilLoja.store';

import Input from 'components/PDV/Input';
import InputTelefone from 'components/PDV/InputTelefonePdv';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import { ConfiguracoesMinhasLojasIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { AutocompleteEndereco } from './components';
import { yupResolver } from './validationForms';

type FormData = {
  nome: string;
  telefone: string;
  email: string;
  numero: string;
  endereco: {
    label: string;
    value: {
      endereco: string;
      cep: string;
      logradouro: string;
      complemento: string;
      bairro: string;
      cidade: string;
      estado: string;
      pais: string;
      latitude: number;
      longitude: number;
    };
  };
};

function comporLabelEndereco(
  e?: {
    value: {
      endereco: string;
      cep: string;
      logradouro: string;
      complemento: string;
      bairro: string;
      cidade: string;
      estado: string;
      pais: string;
      latitude: number;
      longitude: number;
    };
  },
  numero?: string
) {
  if (!e?.value) return '';
  const v = e.value;
  const partes = [
    v.logradouro || v.endereco,
    numero?.trim(),
    v.bairro,
    v.cidade && (v.estado ? `${v.cidade} - ${v.estado}` : v.cidade),
  ].filter(Boolean);
  return partes.join(', ');
}

export const PerfilLoja = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const { setDados, dados, obteveDadosAPI, setObteveDadosAPI } =
    usePerfilLoja();
  const [numeroEnderecoBloqueado, setNumeroEnderecoBloqueado] = useState(true);
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: {
      ...dados,
      endereco: dados.endereco ?? null,
      numero: '',
    } as FormData,
  });

  const { handleSubmit, setValue, watch } = formMethods;

  const handleAvancar = handleSubmit(async (data: FormData) => {
    setIsLoading(true);
    const { endereco } = data;
    const labelEndereco = comporLabelEndereco(endereco, data.numero);

    const novosDados = {
      nome: data.nome,
      telefone: data.telefone,
      email: data.email,
      endereco: {
        label: labelEndereco,
        value: {
          endereco: labelEndereco,
          cep: endereco.value?.cep,
          logradouro: endereco.value?.logradouro,
          complemento: endereco.value?.complemento,
          bairro: endereco.value?.bairro,
          cidade: endereco.value?.cidade,
          estado: endereco.value?.estado,
          pais: endereco.value?.pais,
          longitude: endereco.value?.longitude,
          latitude: endereco.value?.latitude,
        },
      },
      numero: data?.numero,
    };
    setDados(novosDados);

    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.DESCRICAO_LOJA);
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IDENTIFICADOR_URL);
  };

  const obterInformacaoSalva = useCallback(async () => {
    setIsLoading(true);

    const dadosLoja = await obterPerfilLojaDelivery();
    if (dadosLoja) {
      const endereco = {
        label: dadosLoja?.endereco?.endereco,
        value: {
          endereco: dadosLoja?.endereco?.endereco,
          cep: dadosLoja.endereco?.cep,
          logradouro: dadosLoja.endereco?.logradouro,
          numero: dadosLoja.endereco?.numero,
          complemento: dadosLoja.endereco?.complemento,
          bairro: dadosLoja.endereco?.bairro,
          cidade: dadosLoja.endereco?.cidade,
          estado: dadosLoja.endereco?.estado,
          pais: dadosLoja.endereco?.pais,
          latitude: dadosLoja.endereco?.latitude,
          longitude: dadosLoja.endereco?.longitude,
        },
      };
      setValue('nome', dadosLoja?.nome);
      setValue('numero', dadosLoja?.endereco?.numero || '');
      setValue('telefone', dadosLoja?.telefone);
      setValue('email', dadosLoja?.email);
      setValue('endereco', endereco);

      const novosDadosSalvos = {
        ...dadosLoja,
        endereco,
      };
      setDados(novosDadosSalvos);
      setObteveDadosAPI();
    }
    setIsLoading(false);
  }, [setIsLoading, setValue, setDados, setObteveDadosAPI]);

  useEffect(() => {
    if (obteveDadosAPI) return;
    obterInformacaoSalva();
  }, [obterInformacaoSalva, obteveDadosAPI]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Perfil da Loja"
          handleVoltar={handleVoltar}
          icon={ConfiguracoesMinhasLojasIcon}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Informe o nome que será exibido no topo do aplicativo e a
              descrição que ficará disponível no perfil da loja para que os
              clientes conheçam seus diferenciais e pratos principais:
            </Text>
          </VStack>

          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <SimpleGridForm w="full">
              <GridItem colSpan={12}>
                <Input
                  id="nome"
                  name="nome"
                  placeholder="Ex: Recanto Lanches e Porções"
                  label="Nome da loja para exibição no cardápio"
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={12}>
                <AutocompleteEndereco
                  name="endereco"
                  id="endereco"
                  label="Endereço"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 6, 6, 6]}>
                <Input
                  id="numero"
                  name="numero"
                  placeholder="Digite o número do endereço"
                  label="Número do endereço"
                  actionLinkOnClick={() => setNumeroEnderecoBloqueado(false)}
                  actionLinkText="Alterar número"
                  isDisabled={numeroEnderecoBloqueado}
                  onChange={(e) => {
                    setValue('numero', (e.target as HTMLInputElement).value, {
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue('endereco', {
                      ...watch('endereco'),
                      value: {
                        ...watch('endereco')?.value,
                        latitude: 0,
                        longitude: 0,
                      },
                    });
                  }}
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 6, 6, 6]}>
                <InputTelefone
                  id="telefone"
                  name="telefone"
                  placeholder="(00) 00000-0000"
                  label="Telefone"
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={12}>
                <Input
                  id="email"
                  name="email"
                  label="E-mail"
                  placeholder="Digite o endereço de e-mail"
                  colSpan={6}
                  maxLength={60}
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
            </SimpleGridForm>
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
