import { Text, VStack, Box, useMediaQuery } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import { atualizarPerfilLojaDelivery } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';
import { usePerfilLoja } from 'store/Cardapio/perfilLoja.store';

import TextareaField from 'components/PDV/Textarea';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import { ConfiguracoesMinhasLojasIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { FormData, yupResolver } from './validationForms';

export const DescricaoLoja = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const { dados, setDados } = usePerfilLoja();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: {
      descricao: dados?.descricao || '',
    },
  });

  const { handleSubmit } = formMethods;

  const handleAvancar = handleSubmit(async ({ descricao }) => {
    setIsLoading(true);
    const { endereco } = dados;

    const response = await atualizarPerfilLojaDelivery({
      descricao,
      email: dados?.email || '',
      nome: dados?.nome || '',
      telefone: dados?.telefone || '',
      endereco: {
        endereco: endereco?.value?.endereco || '',
        cep: endereco?.value?.cep || '',
        logradouro: endereco?.value?.logradouro || '',
        numero: dados?.numero || '',
        complemento: endereco?.value?.complemento || '',
        bairro: endereco?.value?.bairro || '',
        cidade: endereco?.value?.cidade || '',
        estado: endereco?.value?.estado || '',
        pais: endereco?.value?.pais || '',
        latitude: endereco?.value?.latitude || 0,
        longitude: endereco?.value?.longitude || 0,
      },
    });
    if (response) {
      setDados({
        descricao,
      });
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_LOGO);
    }
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.PERFIL_LOJA);
  };

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Descrição da Loja"
          handleVoltar={handleVoltar}
          icon={ConfiguracoesMinhasLojasIcon}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Informe o nome que será exibido no topo do aplicativo e a
              descrição que ficará disponível no perfil da loja para que os
              clientes conheçam seus diferenciais e pratos principais:
            </Text>
          </VStack>
          <VStack w="full" alignItems="center">
            <TextareaField
              name="descricao"
              id="descricao"
              placeholder="Descreva o seu estabelecimento e suas principais características."
              label="Texto de descrição"
              border="1px solid"
              borderColor="gray.100"
              minH="280px"
              maxH="560px"
              _placeholder={{ color: 'gray.200' }}
            />
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
