import {
  Text,
  VStack,
  Box,
  useMediaQuery,
  Button,
  Flex,
  Icon,
} from '@chakra-ui/react';
import { useCallback, useEffect, useRef } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  atualizarImagensSelecaoDelivery,
  obterImagemSelecaoLojas,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import {
  IconeEtapaSelecaoLojaWeb,
  IconeEtapaSelecaoLojaMobile,
} from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import {
  ConfiguracoesPadronizacaoIcon,
  ImagemAdicionarIcon,
  LixeiraIcon,
} from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';
import { processarImagemUpload } from '../utils/imagem';

export const ImagemLojas = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm();
  const { handleSubmit, watch, setValue } = formMethods;

  const imagemWeb = watch('imagemWeb');
  const imagemMobile = watch('imagemMobile');
  const inputFileImagemWebRef = useRef<HTMLInputElement>(null);
  const inputFileImagemMobileRef = useRef<HTMLInputElement>(null);

  const handleAvancar = handleSubmit(async (data) => {
    setIsLoading(true);
    const { imagemWeb, imagemMobile } = data;

    const imagemWebAtualizada = imagemWeb?.startsWith('data:image')
      ? imagemWeb?.split(',')[1]
      : imagemWeb;

    const imagemMobileAtualizada = imagemMobile?.startsWith('data:image')
      ? imagemMobile.split(',')[1]
      : imagemMobile;

    const response = await atualizarImagensSelecaoDelivery(
      imagemWebAtualizada || '',
      imagemMobileAtualizada || ''
    );

    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.COR_PERSONALIZADA);
    }
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_CAPA);
  };

  const removerImagem = (nomeImagem: string) => {
    setValue(nomeImagem, '');
  };

  const salvarImagem = async (nomeImagem: string, imagem: string) => {
    setValue(nomeImagem, imagem);
  };

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    nomeImagem: string,
    resolucaoMaxima: {
      widthImage: number;
      heightImage: number;
    }
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await processarImagemUpload(
      file,
      nomeImagem,
      resolucaoMaxima,
      salvarImagem
    );
  };

  const buscarImagensSelecaoLoja = useCallback(async () => {
    setIsLoading(true);
    const response = await obterImagemSelecaoLojas();
    const { mobile, web } = response || {};
    if (web) {
      setValue('imagemWeb', web);
    }
    if (mobile) {
      setValue('imagemMobile', mobile);
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    buscarImagensSelecaoLoja();
  }, [buscarImagensSelecaoLoja]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Imagem para seleção de lojas"
          handleVoltar={handleVoltar}
          icon={ConfiguracoesPadronizacaoIcon}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack
            mb={['0', '22px', '22px']}
            color="primary.50"
            fontSize="14px"
            justifyContent="center"
            spacing="24px"
          >
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
              whiteSpace="pre-line"
              w={isLargerThan700 ? '560px' : 'full'}
            >
              {`Quando houver duas ou mais lojas dentro da mesma conta é necessário definir uma imagem de background para a tela de seleção das lojas. Essa tela exibe uma lista com todas as lojas existentes na conta principal e o usuário poderá selecionar em qual loja ele deseja efetuar o pedido.
              → Utilize imagens com extensão JPG, JPEG ou PNG
              → Tamanho máximo do arquivo de 500 kb`}
            </Text>
          </VStack>
          <Flex flexDir="column" w="full" align="center">
            <VStack
              color="black"
              fontSize="14px"
              justifyContent="center"
              spacing="24px"
              mt="56px"
              borderBottom="1px solid"
              borderColor="gray.200"
              maxW="480px"
              align="center"
              pb="32px"
              flexWrap="wrap"
            >
              <Flex
                gap="20px"
                align="center"
                flexWrap="wrap"
                w="full"
                flexDir={['column', 'row', 'row']}
              >
                <Box w="40px">
                  <IconeEtapaSelecaoLojaWeb width="40px" height="40px" />
                </Box>
                <Flex flexDir="column" w="175px" letterSpacing="0px">
                  <Text>Imagem para versão web</Text>
                  <Text>
                    Resolução:{' '}
                    <Text as="span" fontWeight="bold">
                      1920 x 1080px
                    </Text>
                  </Text>
                </Flex>
                <Flex align="center" gap="8px">
                  <Button
                    leftIcon={<ImagemAdicionarIcon strokeWidth="2px" />}
                    color="white"
                    colorScheme={imagemWeb ? 'blue' : 'aquamarine'}
                    w="200px"
                    height="32px"
                    fontWeight="normal"
                    fontSize="14px"
                    onClick={() => {
                      if (inputFileImagemWebRef.current) {
                        inputFileImagemWebRef.current.click();
                      }
                    }}
                  >
                    {imagemWeb ? 'Alterar imagem' : 'Adicionar imagem'}
                  </Button>
                  <input
                    tabIndex={-1}
                    onChange={(event) => {
                      handleFileChange(event, 'imagemWeb', {
                        widthImage: 1920,
                        heightImage: 1080,
                      });
                    }}
                    style={{
                      display: 'none',
                    }}
                    multiple={false}
                    ref={inputFileImagemWebRef}
                    type="file"
                    accept="image/webp, image/avif, image/png, image/jpeg, image/jpg"
                  />
                  {imagemWeb ? (
                    <Icon
                      as={LixeiraIcon}
                      boxSize="16px"
                      cursor="pointer"
                      onClick={() => {
                        removerImagem('imagemWeb');
                      }}
                    />
                  ) : (
                    <Box w="16px" />
                  )}
                </Flex>
              </Flex>
            </VStack>
            <VStack
              mb={['0', '22px', '22px']}
              color="black"
              fontSize="14px"
              justifyContent="center"
              spacing="24px"
              mt="28px"
              borderBottom="1px solid"
              borderColor="gray.200"
              maxW="480px"
              align="center"
              pb="32px"
              flexWrap="wrap"
            >
              <Flex
                gap="20px"
                align="center"
                flexWrap="wrap"
                w="full"
                flexDir={['column', 'row', 'row']}
              >
                <Box w="40px">
                  <IconeEtapaSelecaoLojaMobile width="40px" height="40px" />
                </Box>
                <Flex flexDir="column" w="175px" letterSpacing="0px">
                  <Text>Imagem para versão mobile</Text>
                  <Text>
                    Resolução:{' '}
                    <Text as="span" fontWeight="bold">
                      1080 x 1920px
                    </Text>
                  </Text>
                </Flex>
                <Flex align="center" gap="8px">
                  <Button
                    leftIcon={<ImagemAdicionarIcon strokeWidth="2px" />}
                    color="white"
                    colorScheme={imagemMobile ? 'blue' : 'aquamarine'}
                    w="200px"
                    height="32px"
                    fontWeight="normal"
                    fontSize="14px"
                    onClick={() => {
                      if (inputFileImagemMobileRef.current) {
                        inputFileImagemMobileRef.current.click();
                      }
                    }}
                  >
                    {imagemMobile ? 'Alterar imagem' : 'Adicionar imagem'}
                  </Button>
                  <input
                    tabIndex={-1}
                    onChange={(event) => {
                      handleFileChange(event, 'imagemMobile', {
                        widthImage: 1080,
                        heightImage: 1920,
                      });
                    }}
                    style={{
                      display: 'none',
                    }}
                    multiple={false}
                    ref={inputFileImagemMobileRef}
                    type="file"
                    accept="image/webp, image/avif, image/png, image/jpeg, image/jpg"
                  />
                  {imagemMobile ? (
                    <Icon
                      as={LixeiraIcon}
                      boxSize="16px"
                      cursor="pointer"
                      onClick={() => {
                        removerImagem('imagemMobile');
                      }}
                    />
                  ) : (
                    <Box w="16px" />
                  )}
                </Flex>
              </Flex>
            </VStack>
          </Flex>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
