import { Text, Box, VStack, useMediaQuery, Flex, Icon } from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { obterDominioDelivery } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { AtencaoIcon } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';
import ConstanteRotas from 'constants/rotas';
import { MenuIntegracoes } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

export const GuiaIntegracao = () => {
  const { setPassoAtual } = useEtapasContext();
  const history = useHistory();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');
  const [avancarEtapaDesabilitado, setAvancarEtapaDesabilitado] =
    useState(false);

  const handleIrParaDominioPersonalizado = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IDENTIFICADOR_URL);
  };

  const voltarParaLojaAplicativos = () => {
    history.push(ConstanteRotas.LOJA_APLICATIVOS);
  };

  const obterDominioDeliverySalvo = useCallback(async () => {
    const dominioDelivery = await obterDominioDelivery();
    setAvancarEtapaDesabilitado(!dominioDelivery);
  }, []);

  useEffect(() => {
    obterDominioDeliverySalvo();
  }, [obterDominioDeliverySalvo]);

  return (
    <>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Guia de integração"
          icon={MenuIntegracoes}
          handleVoltar={voltarParaLojaAplicativos}
        />
        <VStack
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          color="primary.50"
          fontSize="14px"
          fontWeight="normal"
          spacing={['10px', '10px', '24px']}
          maxW="600px"
        >
          <Text textAlign="left" letterSpacing="0px">
            Siga as instruções do nosso guia de integração para configurar a
            exibição do aplicativo. Seguindo os passos de configuração você irá
            personalizar o layout do app com a cor principal e as imagens da sua
            loja e na sequência irá selecionar quais produtos deseja exibir no
            aplicativo.
          </Text>
          <Box w="full">
            <Text textAlign="left" letterSpacing="0px">
              Siga as orientações e preencha corretamente as informações
              solicitadas.
            </Text>
          </Box>
          <Box w="full">
            <Text textAlign="left" letterSpacing="0px">
              Para qualquer dúvida consulte os conteúdos de apoio em nossa
              central de ajuda ou fale com um especialista de suporte técnico.
            </Text>
          </Box>
        </VStack>
        {avancarEtapaDesabilitado && (
          <VStack
            color="black"
            mt="26px"
            fontSize="14px"
            fontWeight="normal"
            spacing={['10px', '10px', '24px']}
            justify="center"
            align="center"
            px={['24px', '80px']}
            bg="yellow.500"
            maxW="560px"
            borderRadius="12px"
            pt="18px"
            py="24px"
          >
            <Flex gap="10px" align="flex-start">
              <Icon as={AtencaoIcon} fontSize="16px" />
              <Text letterSpacing="0px" bg="yellow.500" w="full">
                No momento{' '}
                <Text as="span" fontWeight="bold">
                  não é possível iniciar a configuração
                </Text>{' '}
                pois a integração não foi vinculada a sua conta. Por gentileza,
                entre em contato com um especialista de suporte técnico.
              </Text>
            </Flex>
          </VStack>
        )}
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '-30px', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleIrParaDominioPersonalizado}
        isDisabledAvancar={avancarEtapaDesabilitado}
      />
    </>
  );
};
