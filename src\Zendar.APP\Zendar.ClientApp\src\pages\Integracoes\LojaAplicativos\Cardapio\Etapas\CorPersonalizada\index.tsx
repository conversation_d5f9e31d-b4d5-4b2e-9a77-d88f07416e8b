import {
  Text,
  VStack,
  Box,
  useMediaQuery,
  FormLabel,
  Flex,
  Button,
} from '@chakra-ui/react';
import { useCallback, useEffect } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';

import auth from 'modules/auth';

import { calcularContraste } from 'helpers/layout/calcularContraste';

import {
  atualizarCorPersonalizada,
  obterCorPersonalizada,
} from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import { ColorPicker } from 'pages/Integracoes/LojaAplicativos/AutoAtendimento/PainelAdministradorAutoAtendimento/Tema/areas/components/ColorPicker';

import { IconeEtapaCorPersonalizada } from 'icons/Integracoes/Cardapio';

import { ETAPA_GUIA_INTEGRACAO_CARDAPIO } from 'constants/enum/EtapasGuiaIntegracaoCardapio';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { FormData, yupResolver } from './validationForms';

export const CorPersonalizada = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');
  const clienteSti3 = auth.obterClienteSTi3();

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    shouldUnregister: false,
  });

  const { handleSubmit, control, setValue } = formMethods;

  const corPersonalizada = useWatch({
    name: 'corPersonalizada',
    control,
  });

  const handleAvancar = handleSubmit(async ({ corPersonalizada }) => {
    setIsLoading(true);
    const response = await atualizarCorPersonalizada(corPersonalizada);
    if (response) {
      setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.TEMPO_PREPARO);
    }

    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(ETAPA_GUIA_INTEGRACAO_CARDAPIO.IMAGEM_SELECAO_LOJA);
  };

  const obterCorPersonalizadaSalva = useCallback(async () => {
    setIsLoading(true);
    const corSalva = await obterCorPersonalizada();
    if (corSalva) {
      setValue('corPersonalizada', corSalva?.toUpperCase());
    } else {
      const corDefaultPorSistema = clienteSti3 ? '#FF005A' : '#003763';
      setValue('corPersonalizada', corDefaultPorSistema);
    }
    setIsLoading(false);
  }, [setValue, setIsLoading]);

  useEffect(() => {
    obterCorPersonalizadaSalva();
  }, [obterCorPersonalizadaSalva]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height="100vh">
        <Header
          title="Cor personalizada"
          handleVoltar={handleVoltar}
          icon={IconeEtapaCorPersonalizada}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="32px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Selecione uma cor de sua preferência para personalizar os botões
              de confirmação dentro do site. Não é possível personalizar outros
              elementos presentes no layout.
            </Text>
          </VStack>
          <Flex
            justify="center"
            gap="40px"
            flexDir={['column', 'row']}
            align={['center', 'flex-start']}
          >
            <Box>
              <FormLabel mb="0" fontSize="sm" color="black">
                Cor Principal
              </FormLabel>
              <Box w="200px">
                <ColorPicker
                  name="corPersonalizada"
                  style={{
                    borderRadius: '5px',
                    borderStyle: 'solid',
                    borderWidth: '2px',
                  }}
                  widthContainer="196px"
                  classNameAmostraCores="swatch_guia_integracao"
                  classNamePicker="picker_guia_integracao"
                />
              </Box>
            </Box>
            <Flex
              flexDir="column"
              h="275px"
              boxShadow="0px 0px 18px #00000029"
              border="2px solid #000000"
              borderRadius="18px"
              bg="white"
              w="160px"
              px="20px"
            >
              <Flex w="full" pt="48px" gap="10px">
                <Flex flexDir="column" gap="10px" mt="4px">
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="24px" h="1px" bg="gray.200" borderRadius="6px" />
                </Flex>
                <Box bg="gray.200" w="32px" h="32px" borderRadius="6px" />
              </Flex>
              <Flex w="full" pt="24px" gap="10px">
                <Flex flexDir="column" gap="10px" mt="4px">
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="24px" h="1px" bg="gray.200" borderRadius="6px" />
                </Flex>
                <Box bg="gray.200" w="32px" h="32px" borderRadius="6px" />
              </Flex>
              <Flex w="full" pt="24px" gap="10px">
                <Flex flexDir="column" gap="10px" mt="4px">
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="75px" h="1px" bg="gray.200" borderRadius="6px" />
                  <Box w="24px" h="1px" bg="gray.200" borderRadius="6px" />
                </Flex>
                <Box bg="gray.200" w="32px" h="32px" borderRadius="6px" />
              </Flex>
              <Button
                bg={corPersonalizada}
                borderRadius="5px"
                color={calcularContraste(corPersonalizada)}
                height="32px"
                fontSize="13px"
                w="118px"
                mt="24px"
                border="1px solid"
                borderColor={calcularContraste(corPersonalizada)}
                _hover={{}}
                _focus={{}}
                _active={{}}
              >
                Confirmar
              </Button>
            </Flex>
          </Flex>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
        isDisabledAvancar={!corPersonalizada}
      />
    </FormProvider>
  );
};
