import { useCallback, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';

import { ModalConfirmarAcao } from 'components/update/Modal/ModalConfirmarAcao';
import { ModalRemoverDadosIntegracao } from 'components/update/Modal/ModalRemoverDadosIntegracao';

import { obterDadosIntegracaoTray } from 'api/Tray/ObterDadosIntegração';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';

export const useTrayData = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [trayDateActivated, setTrayDateActivated] = useState<Date | undefined>(
    new Date()
  );

  const history = useHistory();
  const possuiServicoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const listItemsInfo = [
    'Para acessar o painel de configurações da integração, clique em: Integrações > Tray',
    'Para reset da configuração ou para excluir esta integração, clique em “cancelar integração” no canto superior direito da página.',
    'Para outras dúvidas ou problemas, acesse nossa central de ajuda ou entre em contato com nossa equipe técnica.',
  ];

  const cancelIntregation = async () => {
    ModalRemoverDadosIntegracao({
      callback: async () => {
        setIsLoading(true);
        const response = await api.post<void, ResponseApi>(
          ConstanteEnderecoWebservice.INTEGRACAO_TRAY_EXCLUIR
        );
        if (response) {
          if (response.avisos) {
            response.avisos.forEach((aviso: string) => toast.warning(aviso));
          }
          if (response.sucesso) {
            history.push(ConstanteRotas.LOJA_APLICATIVOS);
          }
          setIsLoading(false);
        }
        setIsLoading(false);
      },
    });
  };

  const confirmCancellation = () => {
    ModalConfirmarAcao({
      mainText: 'Excluir ou Reiniciar',
      submitButtonText: 'Continuar',
      secondaryText:
        'Todos os dados armazenados e todo o processo de configuração serão excluídos. Essa ação não poderá ser desfeita. Deseja continuar?',
      variant: 'danger',
      callbackSubmit: cancelIntregation,
    });
  };

  const lidarComDadosIntegracaoTray = useCallback(async () => {
    setIsLoading(true);

    const response = await obterDadosIntegracaoTray();

    const integracaoPossuiDados = !!response?.dados;
    const naoPossuiServicoTray = !possuiServicoTray;

    if (!response || (naoPossuiServicoTray && integracaoPossuiDados)) {
      setTrayDateActivated(undefined);
      setIsLoading(false);
      return;
    }

    if (response.sucesso) {
      setTrayDateActivated(response.dados.dataAtivacao);
      setIsLoading(false);
      return;
    }

    if (response.avisos) {
      setIsLoading(false);
      history.push(
        possuiServicoTray
          ? ConstanteRotasAlternativas.TRAY_ETAPAS
          : ConstanteRotas.INTEGRACAO_TRAY_TELA_COMERCIAL
      );
    }
  }, [history, possuiServicoTray]);

  useEffect(() => {
    lidarComDadosIntegracaoTray();
  }, [lidarComDadosIntegracaoTray]);

  return {
    trayDateActivated,
    listItemsInfo,
    handleCancel: confirmCancellation,
    isLoading,
  };
};
