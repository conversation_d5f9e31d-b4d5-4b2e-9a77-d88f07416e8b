import { VStack, Text, Link } from '@chakra-ui/react';
import { useCallback, useEffect, useImperativeHandle } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { buscarDadosAutenticacao, autenticarDados } from 'services/tray';

import { ContainerIntegracaoFixo } from 'store/Cardapio/Etapas/EtapasContext';
import { useTrayEtapasContext } from 'store/Tray';

import Input from 'components/PDV/Input';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { CadeadoAutenticacaoIcon } from 'icons';

import { Header } from '../Layout/Header';

import { yupResolver, FormData } from './validationForms';

export const Autenticacao = () => {
  const { ref, setActiveStep, setIsLoading } = useTrayEtapasContext();

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
  });

  const { handleSubmit: onSubmit, reset } = formMethods;

  const handleAvancar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.CANAL_VENDAS);
  }, [setActiveStep]);

  const handleVoltar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.PORTAL_CLIENTE);
  }, [setActiveStep]);

  const handleSubmit = onSubmit(async (data) => {
    setIsLoading(true);
    const urlComWebApi = data.url.endsWith('/web_api')
      ? data.url
      : `${data.url.replace(/\/$/, '')}/web_api`;
    const response = await autenticarDados({
      ...data,
      url: urlComWebApi,
    });

    if (response !== null) {
      if (response === true) {
        handleAvancar();
      }
    }

    setIsLoading(false);
  });

  const getAutenticacao = useCallback(async () => {
    setIsLoading(true);

    const response = await buscarDadosAutenticacao();

    if (response !== null) {
      reset(response);
    }

    setIsLoading(false);
  }, [setIsLoading, reset]);

  useImperativeHandle(ref, () => ({
    handleAvancar: handleSubmit,
    handleVoltar,
  }));

  useEffect(() => {
    getAutenticacao();
  }, [getAutenticacao]);

  return (
    <ContainerIntegracaoFixo>
      <Header
        title="Autenticação"
        handleVoltar={handleVoltar}
        icon={CadeadoAutenticacaoIcon}
      />
      <FormProvider {...formMethods}>
        <VStack
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          color="purple.500"
          fontSize="14px"
          fontWeight="normal"
          spacing="24px"
          maxW="600px"
        >
          <Text mb="4px" textAlign="left" letterSpacing="0px">
            Ainda no{' '}
            <Link
              href="https://www.tray.com.br/"
              target="_blank"
              style={{ textDecoration: 'underline', cursor: 'pointer' }}
              _hover={{ color: 'purple.400' }}
            >
              portal do cliente Tray,
            </Link>{' '}
            (dentro do aplicativo PowerStock Cloud) clique no botão “Acessar”.
            Na tela seguinte clique em “Autorizar”. Copie e cole os parâmetros
            da Tray nos campos abaixo. São dados personalizados e essenciais
            para o funcionamento da integração.
          </Text>

          <Input
            name="url"
            label="Domínio da loja (URL)"
            placeholder="https://nomedaloja.com.br"
            colSpan={12}
          />
          <Input
            id="code"
            name="code"
            label="Code"
            colSpan={12}
            placeholder="Informe o identificador da integração"
          />
        </VStack>
      </FormProvider>
    </ContainerIntegracaoFixo>
  );
};
