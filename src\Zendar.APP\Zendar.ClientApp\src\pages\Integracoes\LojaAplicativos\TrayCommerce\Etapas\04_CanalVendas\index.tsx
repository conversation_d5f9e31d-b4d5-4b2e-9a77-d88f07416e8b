import { VStack, Text } from '@chakra-ui/react';
import { useCallback, useImperativeHandle, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { getCanalVendasTray, getCadastrarCanalVendasTray } from 'services/tray';

import { ContainerIntegracaoFixo } from 'store/Cardapio/Etapas/EtapasContext';
import { useTrayEtapasContext } from 'store/Tray';

import Input from 'components/PDV/Input';

import { ETAPA_GUIA_INTEGRACAO_TRAY } from 'constants/enum/IdentificacaoEtapasTray';
import { ConfiguracoesMinhasLojasIcon } from 'icons';

import { Header } from '../Layout/Header';

import { yupResolver, FormData } from './validationForms';

export const CanalVendas = () => {
  const { ref, setActiveStep, setIsLoading } = useTrayEtapasContext();

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
  });

  const { handleSubmit: onSubmit, reset } = formMethods;

  const handleAvancar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.VENDEDOR);
  }, [setActiveStep]);

  const handleVoltar = useCallback(() => {
    setActiveStep(ETAPA_GUIA_INTEGRACAO_TRAY.AUTENTICACAO);
  }, [setActiveStep]);

  const getCanalVendas = useCallback(async () => {
    setIsLoading(true);

    const response = await getCanalVendasTray<FormData>();

    if (response !== null) {
      reset(response);
      setIsLoading(false);
    }

    setIsLoading(false);
  }, [setIsLoading, reset]);

  const handleSubmit = onSubmit(async (data) => {
    setIsLoading(true);

    const response = await getCadastrarCanalVendasTray(data);

    if (response !== null) {
      if (response === true) {
        handleAvancar();
      }
    }

    setIsLoading(false);
  });

  useImperativeHandle(ref, () => ({
    handleAvancar: handleSubmit,
    handleVoltar,
  }));

  useEffect(() => {
    getCanalVendas();
  }, [getCanalVendas]);

  return (
    <ContainerIntegracaoFixo>
      <Header
        title="Canal de venda"
        handleVoltar={handleVoltar}
        icon={ConfiguracoesMinhasLojasIcon}
      />
      <FormProvider {...formMethods}>
        <VStack
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          color="purple.500"
          fontSize="14px"
          fontWeight="normal"
          w="600px"
          gap="24px"
        >
          <Text w="full" mb="4px" textAlign="left" letterSpacing="0px">
            Para identificar esta conta, informe o nome da sua loja.
          </Text>

          <Input
            id="nomeCanalVenda"
            name="nomeCanalVenda"
            label="Nome do canal de venda"
            colSpan={12}
            placeholder="Ex: Belíssima Calçados"
          />
        </VStack>
      </FormProvider>
    </ContainerIntegracaoFixo>
  );
};
