import { useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';
import { CellMeasurerCache, ScrollParams } from 'react-virtualized';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import { useProdutosEdicaEmMassaStore } from 'store/Produtos/ProdutosEdicaoEmMassa.store';

import { useQuery } from 'hooks/useQuery';

import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Paginacao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { AlteracaoEmMassaProdutoCamposEditaveisEnum } from 'constants/enum/camposEditaveisProdutoAlteracaoEmMassa';
import ConstanteRotas from 'constants/rotas';

import { KEY_FILTRAR } from '../Modal/hooks';
import { EdicaoEmMassaFiltrarEnum } from '../Passos/EscolherFiltro';

const TOTAL_PAGINAS = 20;

export const useEdicaoEmMassaSelecionarProdutos = () => {
  const {
    filtrosAplicados,
    produtosIds,
    setProdutosIds,
    selecionados,
    setSelecionados,
    nomeCampo,
    valorAtual,
  } = useProdutosEdicaEmMassaStore();

  const history = useHistory();
  const { query, obterQueryParam } = useQuery(history);

  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const tipoFiltro = (obterQueryParam(KEY_FILTRAR) ??
    undefined) as EdicaoEmMassaFiltrarEnum;

  const camposQuePodemSerNulos = [
    AlteracaoEmMassaProdutoCamposEditaveisEnum.PRODUTO_GERENCIADOR_IMPRESSAO,
    AlteracaoEmMassaProdutoCamposEditaveisEnum.PRODUTO_CEST,
    AlteracaoEmMassaProdutoCamposEditaveisEnum.PRODUTO_CODIGO_BENEFICIO_FISCAL,
  ];

  const [totalRegistros, setTotalRegistros] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [naoSelecionarAutomaticamente, setNaoSelecionarAutomaticamente] =
    useState(false);
  const [produtos, setProdutos] = useState<
    { nome: string; id: string; selecionado: boolean }[]
  >([]);

  const paginaAtual = useRef(1);
  const cache = useRef(
    new CellMeasurerCache({
      fixedWidth: true,
      defaultHeight: 54.6,
    })
  );

  const todosProdutosSelecionados = produtos.every(
    ({ selecionado }) => selecionado
  );

  const obterTotalProdutosSelecionados = useCallback(() => {
    const possuiProdutoSalvos = produtosIds.length > 0;

    if (naoSelecionarAutomaticamente) {
      return produtos.filter(({ selecionado }) => selecionado).length;
    }

    if (!possuiProdutoSalvos) {
      const valor =
        totalRegistros -
        produtos.filter(({ selecionado }) => !selecionado).length;

      return valor;
    }

    const quantidadeProdutosSalvos = produtosIds.filter(
      (value) => !produtos.some((produto) => produto.id === value)
    ).length;

    const valor = selecionados
      ? quantidadeProdutosSalvos +
        produtos.filter((produto) => produto.selecionado).length
      : totalRegistros -
        quantidadeProdutosSalvos -
        produtos.filter((produto) => !produto.selecionado).length;

    return valor;
  }, [produtosIds, produtos, totalRegistros]);

  const totalProdutosSelecionados = obterTotalProdutosSelecionados();

  const redirecionarParaEdicaoEmMassa = () => {
    history.push({
      pathname: ConstanteRotas.PRODUTO_EDICAO_EM_MASSA,
      search: query.toString(),
    });
  };

  const salvarProdutos = useCallback(() => {
    const possuiMaisProdutosSelecionados =
      obterTotalProdutosSelecionados() > totalRegistros / 2;

    const novosProdutosIds = produtos
      .filter(({ selecionado }) =>
        possuiMaisProdutosSelecionados ? !selecionado : selecionado
      )
      .map(({ id }) => id);

    setSelecionados(!possuiMaisProdutosSelecionados);
    setProdutosIds(novosProdutosIds);
  }, [produtos, obterTotalProdutosSelecionados]);

  const handleSalvarProdutosSelecionados = () => {
    salvarProdutos();
    redirecionarParaEdicaoEmMassa();
  };

  const produtosPorCategoriaMarca = async (
    gridPaginadaConsulta: GridPaginadaConsulta
  ) => {
    const filtros = {
      categoriasProduto: filtrosAplicados?.categorias.map(
        (categoria) => categoria.value
      ),
      marcas: filtrosAplicados?.marcas.map((marca) => marca.value),
      statusConsulta: filtrosAplicados?.status,
    };

    const response = await api.post<
      void,
      ResponseApi<
        GridPaginadaRetorno<{
          id: string;
          nome: string;
        }>
      >
    >(
      formatQueryPagegTable(
        ConstanteEnderecoWebservice.PRODUTO_LISTAR_PAGINADO,
        gridPaginadaConsulta
      ),
      {
        ...filtros,
      }
    );

    return response;
  };

  const produtosPorValorEspecifico = async (
    gridPaginadaConsulta: GridPaginadaConsulta
  ) => {
    const filtros = {
      nomeCampo,
      valor: valorAtual,
    };

    if (filtros.valor && typeof filtros.valor === 'object') {
      filtros.valor = filtros.valor.value;
    }

    const response = await api.post<
      void,
      ResponseApi<
        GridPaginadaRetorno<{
          id: string;
          nome: string;
        }>
      >
    >(
      formatQueryPagegTable(
        ConstanteEnderecoWebservice.PRODUTOS_ALTERACAO_EM_MASSA_LISTAR_PAGINADO,
        gridPaginadaConsulta
      ),
      {
        ...filtros,
      }
    );

    return response;
  };

  const obterProdutos = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      let response: ResponseApi<
        GridPaginadaRetorno<{
          id: string;
          nome: string;
        }>
      >;

      if (tipoFiltro === EdicaoEmMassaFiltrarEnum.POR_CATEGORIA_MARCA) {
        response = await produtosPorCategoriaMarca(gridPaginadaConsulta);
      } else {
        response = await produtosPorValorEspecifico(gridPaginadaConsulta);
      }

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response.dados) {
          const possuiProdutoSalvos = produtosIds.length > 0;

          setTotalRegistros(response.dados.total);
          setProdutos((prev) => {
            const novosProdutos = [
              ...prev,
              ...response.dados.registros.map(({ id, nome }) => {
                const selecionado = naoSelecionarAutomaticamente
                  ? false
                  : !possuiProdutoSalvos ||
                    (selecionados
                      ? produtosIds.includes(id)
                      : !produtosIds.includes(id));

                return {
                  id,
                  nome,
                  selecionado,
                };
              }),
            ];

            return novosProdutos;
          });
        }
      }

      setIsLoading(false);
    },
    [produtosIds, tipoFiltro, selecionados]
  );

  const alternarSelecaoProduto = (id: string) => {
    setProdutos((prev) => {
      const valor = prev.map((produto) => ({
        ...produto,
        selecionado:
          produto.id === id ? !produto.selecionado : produto.selecionado,
      }));

      return valor;
    });
  };

  const alternarSelecaoTodosProduto = () => {
    setProdutos((prev) => {
      const novosProdutos = prev.map((produto) => ({
        ...produto,
        selecionado: !todosProdutosSelecionados,
      }));

      setNaoSelecionarAutomaticamente(
        novosProdutos.every(({ selecionado }) => !selecionado)
      );

      return novosProdutos;
    });
  };

  const handleScrollListaVirtualizada = (e: ScrollParams) => {
    const possuiMaisRegistros =
      totalRegistros / TOTAL_PAGINAS > paginaAtual.current;
    const ultimoItemVisivel =
      Math.round(e.scrollTop) >= e.scrollHeight - e.clientHeight;

    if (ultimoItemVisivel && possuiMaisRegistros) {
      paginaAtual.current += 1;

      obterProdutos({
        currentPage: paginaAtual.current,
        pageSize: TOTAL_PAGINAS,
        orderColumn: 'nome',
        orderDirection: 'asc',
      });
    }
  };

  const validarValoresParaAlteracao = useCallback(() => {
    const valoresInvalidos =
      !nomeCampo ??
      (valorAtual === null &&
        !camposQuePodemSerNulos.includes(
          nomeCampo as AlteracaoEmMassaProdutoCamposEditaveisEnum
        ));

    if (
      tipoFiltro === EdicaoEmMassaFiltrarEnum.POR_VALOR_ESPECIFICO &&
      valoresInvalidos
    ) {
      redirecionarParaEdicaoEmMassa();
      return;
    }

    obterProdutos({
      currentPage: 1,
      pageSize: TOTAL_PAGINAS,
      orderColumn: 'nome',
      orderDirection: 'asc',
    });
  }, [nomeCampo, valorAtual, obterProdutos]);

  useEffect(() => {
    validarValoresParaAlteracao();
  }, [validarValoresParaAlteracao]);

  return {
    isLoading,
    produtos,
    todosProdutosSelecionados,
    handleSalvarProdutosSelecionados,
    alternarSelecaoProduto,
    alternarSelecaoTodosProduto,
    handleScrollListaVirtualizada,
    redirecionarParaEdicaoEmMassa,
    totalRegistros,
    cache,
    isLargerThan900,
    totalProdutosSelecionados,
  };
};
