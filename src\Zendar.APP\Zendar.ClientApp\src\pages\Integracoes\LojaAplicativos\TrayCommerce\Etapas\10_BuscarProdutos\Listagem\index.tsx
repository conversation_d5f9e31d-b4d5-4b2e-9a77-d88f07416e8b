import { Box, Flex } from '@chakra-ui/react';
import {
  CellMeasurerCache,
  CellMeasurerCacheParams,
  List,
  ListProps,
} from 'react-virtualized';

type VirtualizedListProps = Omit<ListProps, 'width' | 'height'> & {
  loadMore?: (shouldLoad: boolean) => void;
  cellMeasurerCacheParams?: CellMeasurerCacheParams;
  colorBgGradient?: string;
  showTopGradient?: boolean;
  showBottomGradient?: boolean;
};

export const VirtualizadaSemBuscarItens = ({
  onScroll,
  loadMore,
  rowRenderer,
  rowCount,
  rowHeight,
  cellMeasurerCacheParams = {
    fixedWidth: true,
    defaultHeight: 52,
  },

  noRowsRenderer = () => (
    <Flex
      color="black"
      flexDirection={['column', 'row', 'row']}
      justifyContent="space-between"
      alignItems={['left', 'center', 'center']}
      mb="5px"
      h="48px"
      bg="white"
      p="10px"
      pl={['2px', '20px', '20px']}
      pr={['10px', '20px', '20px']}
      borderRadius="6px"
    >
      Nenhum item foi encontrado.
    </Flex>
  ),
  ...props
}: VirtualizedListProps) => {
  const cache = new CellMeasurerCache(cellMeasurerCacheParams);

  return (
    <Box position="relative" w="full" p="1px" h="400px" overflow="scroll">
      <List
        {...props}
        noRowsRenderer={noRowsRenderer}
        rowRenderer={(rowProps) => rowRenderer(rowProps)}
        rowCount={rowCount}
        rowHeight={cache.rowHeight}
        autoHeight
        autoWidth
        width={800}
        height={rowCount * rowHeight}
        deferredMeasurementCache={cache}
        onScroll={(e) => {
          const isLastItemVisible =
            Math.round(e.scrollTop) >= e.scrollHeight - e.clientHeight;

          if (loadMore) {
            loadMore(isLastItemVisible);
          }
          onScroll?.(e);
        }}
      />
    </Box>
  );
};
