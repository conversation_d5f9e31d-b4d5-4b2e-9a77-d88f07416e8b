import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Paginacao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

type CampoPersonalizadoProps = {
  campoPersonalizadoId: string;
  valor: number | string | null;
};

type ObterListaProdutosSemCadastroTrayProps = {
  dadosPaginacao: GridPaginadaConsulta;
  parametros: {
    nomeReferencia: string;
    sku: string;
    codigoBarras: string;
    tipoEstoque: number;
    statusConsulta: number;
    cores: null | string[];
    tamanhos: null | string[];
    categoriasProduto: null | string[];
    marcas: null | string[];
    tags: null | string[];
    camposPersonalizados?: CampoPersonalizadoProps[];
  };
};

export type ProdutoSemCadastroTrayProps = {
  ativo: boolean;
  id: string;
  nome: string;
  tipoProduto: number;
  listaProdutoCorTamanhoId: string[];
  produtoCores: {
    produtoCorTamanhos: {
      produtoCorTamanho: {
        produtoCorTamanhoId: string;
      };
    }[];
  }[];
  estaSelecionado: boolean;
};

export const obterListaProdutosSemCadastroTray = ({
  dadosPaginacao,
  parametros,
}: ObterListaProdutosSemCadastroTrayProps) => {
  const endpoint =
    ConstanteEnderecoWebservice.INTEGRACAO_TRAY_OBTER_LISTA_PRODUTO_PAGINADA;

  return api.post<
    void,
    ResponseApi<GridPaginadaRetorno<ProdutoSemCadastroTrayProps>>
  >(formatQueryPagegTable(endpoint, dadosPaginacao), {
    ...parametros,
  });
};
